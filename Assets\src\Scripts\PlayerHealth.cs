using UnityEngine;
using UnityEngine.Events;
using Sirenix.OdinInspector;
using BloodSystem;

/// <summary>
/// Player-specific health component that extends HealthComponent.
/// Handles health regeneration, stat integration, and player death behavior.
/// Does NOT use pooling (unlike CombatantHealth).
/// </summary>
public class PlayerHealth : HealthComponent
{
    [Title("Health Regeneration")]
    [SerializeField, Tooltip("Base health regeneration per second")]
    private float baseHealthRegen = 0f;
    
    [Title("Player-Specific Events")]
    public UnityEvent<float, float> OnHealthChanged { get; } = new UnityEvent<float, float>();
    
    [Title("Stat Integration")]
    [SerializeField, Tooltip("Reference to PlayerStats for stat calculations")]
    private PlayerStats playerStats;
    
    
    // Internal state
    private float healthRegenTimer = 0f;
    
    // Properties for external access (compatible with UI)
    public float currentHealth => CurrentHealth;
    public float maxHealth => MaxHealth;
    
    protected override void Awake()
    {
        base.Awake();
        
        // Initialize splatter defaults for player
        m_useHitSplatter = true;
        m_useDeathSplatter = true;
        
        // Auto-find PlayerStats if not assigned
        if (playerStats == null)
        {
            playerStats = GetComponent<PlayerStats>();
        }
        
        // Subscribe to events
        OnDeath.AddListener(HandlePlayerDeath);
        
        // Subscribe to MaxHealth changes from PlayerStats
        if (playerStats != null)
        {
            playerStats.OnMaxHealthChanged.AddListener(OnMaxHealthChangedFromStats);
        }
    }
    
    private void Start()
    {
        // Initialize health with calculated values if PlayerStats is available
        if (playerStats != null)
        {
            float calculatedMaxHealth = playerStats.GetCalculatedStat(StatType.MaxHealth);
            if (calculatedMaxHealth > 0f)
            {
                SetMaxHealth(calculatedMaxHealth, true);
            }
            else
            {
                // Fallback if calculated health is invalid
                Debug.LogWarning("PlayerHealth: Calculated max health is 0 or negative, using fallback value of 100");
                SetMaxHealth(100f, true);
            }
        }
        else
        {
            // Fallback if PlayerStats is not available
            Debug.LogWarning("PlayerHealth: PlayerStats is null, using fallback max health of 100");
            SetMaxHealth(100f, true);
        }
        
        // Fire initial UI event to sync with any already connected UI
        OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);
    }
    
    private void Update()
    {
        // Handle health regeneration
        HandleHealthRegeneration();
    }
    
    private void HandleHealthRegeneration()
    {
        float calculatedHealthRegen = GetCalculatedHealthRegen();
        
        if (calculatedHealthRegen > 0 && CurrentHealth < MaxHealth)
        {
            healthRegenTimer += Time.deltaTime;
            if (healthRegenTimer >= 1f)
            {
                Heal(calculatedHealthRegen);
                healthRegenTimer = 0f;
            }
        }
    }
    
    /// <summary>
    /// Get calculated health regeneration considering stat modifiers
    /// </summary>
    private float GetCalculatedHealthRegen()
    {
        if (playerStats != null)
        {
            return playerStats.GetCalculatedStat(StatType.HealthRegen);
        }
        return baseHealthRegen;
    }
    
    /// <summary>
    /// Get calculated max health considering stat modifiers
    /// </summary>
    public float GetCalculatedMaxHealth()
    {
        if (playerStats != null)
        {
            return playerStats.GetCalculatedStat(StatType.MaxHealth);
        }
        return MaxHealth;
    }
    
    public override void TakeDamage(DamageInfo damageInfo)
    {
        if (CurrentHealth <= 0) return;
        
        // Apply dodge chance if PlayerStats is available
        if (playerStats != null)
        {
            float calculatedDodge = playerStats.GetCalculatedStat(StatType.DodgeChance);
            if (Random.Range(0f, 100f) < calculatedDodge)
            {
                // Dodged! No damage taken
                return;
            }
            
            // Apply defense
            float calculatedDefense = playerStats.GetCalculatedStat(StatType.Defense);
            damageInfo.amount = Mathf.Max(0.01f, damageInfo.amount - calculatedDefense);
        }
        
        // Store health before damage
        float previousHealth = CurrentHealth;
        
        // Call base implementation
        base.TakeDamage(damageInfo);
        
        // Fire UI-compatible event if health actually changed
        if (CurrentHealth != previousHealth)
        {
            OnHealthChanged?.Invoke(CurrentHealth, GetCalculatedMaxHealth());
        }
        
        // Spawn hit splatter
        if (useHitSplatter && BloodSplatterManager.Instance != null)
        {
            BloodSplatterManager.Instance.SpawnHitSplatter(transform.position, Vector3.zero);
        }
    }
    
    /// <summary>
    /// Heal the player and fire UI events
    /// </summary>
    public new void Heal(float healAmount)
    {
        if (healAmount <= 0) return;
        
        float previousHealth = CurrentHealth;
        float calculatedMaxHealth = GetCalculatedMaxHealth();
        
        // Update max health if it changed due to stat calculations
        if (!Mathf.Approximately(MaxHealth, calculatedMaxHealth))
        {
            SetMaxHealth(calculatedMaxHealth, false);
        }
        
        // Heal using base implementation
        base.Heal(healAmount);
        
        // Fire UI-compatible event if health changed
        if (CurrentHealth != previousHealth)
        {
            OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);
        }
    }
    
    /// <summary>
    /// Handle player-specific death behavior
    /// </summary>
    private void HandlePlayerDeath()
    {
        // Spawn death splatter
        if (useDeathSplatter && BloodSplatterManager.Instance != null)
        {
            BloodSplatterManager.Instance.SpawnDeathSplatter(transform.position);
        }
        
        // Fire UI event
        OnHealthChanged?.Invoke(0f, GetCalculatedMaxHealth());
        
        // TODO: Implement game over screen/logic
        // For now, just log the death
        if (enableDebugLogging)
            Debug.Log("Player died! TODO: Show game over screen");
        
        // Placeholder for future game over implementation
        TriggerGameOver();
    }
    
    /// <summary>
    /// Triggers game over through GameOverManager
    /// </summary>
    private void TriggerGameOver()
    {
        // Trigger game over UI through GameOverManager
        if (GameOverManager.Instance != null)
        {
            GameOverManager.Instance.TriggerGameOver();
        }
        else
        {
            Debug.LogError("GameOverManager instance not found! Cannot show game over screen.");
            
            // Fallback: pause game manually
            Time.timeScale = 0f;
        }
    }
    
    /// <summary>
    /// Called when player respawns - reset health and state
    /// </summary>
    public void OnPlayerRespawn()
    {
        float calculatedMaxHealth = GetCalculatedMaxHealth();
        SetMaxHealth(calculatedMaxHealth, true); // Restore to full health
        healthRegenTimer = 0f;
        
        // Fire UI event
        OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);
    }
    
    /// <summary>
    /// Update max health when stats change (called by PlayerStats)
    /// </summary>
    public void RefreshMaxHealth()
    {
        float calculatedMaxHealth = GetCalculatedMaxHealth();
        if (!Mathf.Approximately(MaxHealth, calculatedMaxHealth))
        {
            SetMaxHealth(calculatedMaxHealth, false); // Don't restore full health
            OnHealthChanged?.Invoke(CurrentHealth, MaxHealth);
        }
    }
    
    /// <summary>
    /// Called when MaxHealth changes in PlayerStats
    /// </summary>
    private void OnMaxHealthChangedFromStats(float newMaxHealth)
    {
        float previousMaxHealth = MaxHealth;
        
        // Update max health without restoring full health
        SetMaxHealth(newMaxHealth, false);
        
        // Fire UI event to update health bar
        OnHealthChanged?.Invoke(CurrentHealth, newMaxHealth);
        
        if (enableDebugLogging)
        {
            Debug.Log($"PlayerHealth: MaxHealth updated from {previousMaxHealth:F1} to {newMaxHealth:F1}");
        }
    }
    
    private void OnDestroy()
    {
        OnDeath.RemoveListener(HandlePlayerDeath);
        
        // Unsubscribe from PlayerStats events
        if (playerStats != null)
        {
            playerStats.OnMaxHealthChanged.RemoveListener(OnMaxHealthChangedFromStats);
        }
    }
    
    #if UNITY_EDITOR
    [Title("Debug Tools")]
    [Button("Take 10 Damage")]
    private void DebugTakeDamage()
    {
        if (Application.isPlaying)
        {
            var damageInfo = DamageInfo.FromSingleType(10f, DamageType.Physical, false, 1f, "Debug");
            TakeDamage(damageInfo);
        }
    }
    
    [Button("Heal 20")]
    private void DebugHeal()
    {
        if (Application.isPlaying)
        {
            Heal(20f);
        }
    }
    
    [Button("Kill Player")]
    private void DebugKill()
    {
        if (Application.isPlaying)
        {
            var damageInfo = DamageInfo.FromSingleType(MaxHealth, DamageType.Physical, false, 1f, "DebugKill");
            TakeDamage(damageInfo);
        }
    }
    
    [Button("Test Respawn")]
    private void DebugRespawn()
    {
        if (Application.isPlaying)
        {
            OnPlayerRespawn();
        }
    }
    
    [Button("Refresh Max Health")]
    private void DebugRefreshMaxHealth()
    {
        if (Application.isPlaying)
        {
            RefreshMaxHealth();
        }
    }
    
    [ShowInInspector]
    [PropertySpace]
    private string HealthStatus => 
        $"Health: {CurrentHealth:F1}/{MaxHealth:F1}\n" +
        $"Health Regen: {GetCalculatedHealthRegen():F2}/s\n" +
        $"PlayerStats: {(playerStats != null ? "Connected" : "Missing")}";
    #endif
}