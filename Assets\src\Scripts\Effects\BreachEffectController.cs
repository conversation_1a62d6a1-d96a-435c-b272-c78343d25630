using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Main controller for breach effects that coordinates separate animation and spawning components.
/// Replaces the monolithic BreachEffect class with a clean, modular architecture.
/// </summary>
[RequireComponent(typeof(CircleCollider2D))]
public class BreachEffectController : MonoBehaviour, ISpawnable
{
    [Title("Breach Effect Controller")]
    [InfoBox("Coordinates material animation, radius animation, and enemy spawning components. Each component can be enabled/disabled independently.", InfoMessageType.Info)]
    
    [Title("Component References")]
    [SerializeField]
    [Tooltip("Material animation component (optional)")]
    private BreachMaterialAnimator materialAnimator;
    
    [SerializeField]
    [Tooltip("Radius animation component (optional)")]
    private BreachRadiusAnimator radiusAnimator;
    
    [SerializeField]
    [Tooltip("Enemy spawning component (optional)")]
    private BreachEnemySpawner enemySpawner;
    
    [Title("General Settings")]
    [SerializeField]
    [Tooltip("Auto-play effects when player triggers the breach")]
    private bool playOnPlayerTrigger = true;
    
    [SerializeField]
    [Tooltip("Auto-play effects on Enable")]
    private bool playOnEnable = false;
    
    [SerializeField]
    [Tooltip("Enable debug logging")]
    private bool enableDebugLogging = false;
    
    // Runtime state
    private CircleCollider2D spatialCollider;
    private bool hasBeenTriggered = false;
    private bool isPlaying = false;
    
    // Properties for external access
    public bool IsPlaying => isPlaying;
    public bool HasBeenTriggered => hasBeenTriggered;
    public BreachMaterialAnimator MaterialAnimator => materialAnimator;
    public BreachRadiusAnimator RadiusAnimator => radiusAnimator;
    public BreachEnemySpawner EnemySpawner => enemySpawner;
    
    #region Unity Lifecycle
    void Awake()
    {
        spatialCollider = GetComponent<CircleCollider2D>();
        
        // Auto-find components if not assigned
        if (materialAnimator == null)
            materialAnimator = GetComponent<BreachMaterialAnimator>();
        if (radiusAnimator == null)
            radiusAnimator = GetComponent<BreachRadiusAnimator>();
        if (enemySpawner == null)
            enemySpawner = GetComponent<BreachEnemySpawner>();
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEffectController] {gameObject.name}: Initialized - Material: {materialAnimator != null}, Radius: {radiusAnimator != null}, Spawner: {enemySpawner != null}");
        }
    }
    
    void OnEnable()
    {
        if (playOnEnable)
        {
            PlayBreachEffect();
        }
    }
    
    void OnDisable()
    {
        StopBreachEffect();
    }
    #endregion
    
    #region Public Methods
    /// <summary>
    /// Starts all enabled breach effect components
    /// </summary>
    [Button("Play Breach Effect", ButtonSizes.Large)]
    public void PlayBreachEffect()
    {
        if (isPlaying)
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[BreachEffectController] {gameObject.name}: Already playing, stopping current effects");
            }
            StopBreachEffect();
        }
        
        isPlaying = true;
        
        // Start material animation
        if (materialAnimator != null && materialAnimator.EnableAnimation)
        {
            materialAnimator.PlayAnimation();
        }
        
        // Start radius animation
        if (radiusAnimator != null && radiusAnimator.EnableAnimation)
        {
            radiusAnimator.PlayAnimation();
        }
        
        // Start enemy spawning
        if (enemySpawner != null && enemySpawner.EnableSpawning)
        {
            enemySpawner.StartSpawning();
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEffectController] {gameObject.name}: Started breach effect - Material: {materialAnimator?.EnableAnimation}, Radius: {radiusAnimator?.EnableAnimation}, Spawning: {enemySpawner?.EnableSpawning}");
        }
    }
    
    /// <summary>
    /// Stops all breach effect components
    /// </summary>
    [Button("Stop Breach Effect", ButtonSizes.Medium)]
    public void StopBreachEffect()
    {
        if (materialAnimator != null)
        {
            materialAnimator.StopAnimation();
        }
        
        if (radiusAnimator != null)
        {
            radiusAnimator.StopAnimation();
        }
        
        if (enemySpawner != null)
        {
            enemySpawner.StopSpawning();
        }
        
        isPlaying = false;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEffectController] {gameObject.name}: Stopped breach effect");
        }
    }
    
    /// <summary>
    /// Resets all components to their start values
    /// </summary>
    [Button("Reset To Start Values", ButtonSizes.Medium)]
    public void ResetToStartValues()
    {
        if (materialAnimator != null)
        {
            materialAnimator.ResetToStartValue();
        }
        
        if (radiusAnimator != null)
        {
            radiusAnimator.ResetToStartRadius();
        }
        
        if (enemySpawner != null)
        {
            // Enemy cleanup now handled by BreachEnemyManager
            // No direct cleanup needed - centralized system handles it
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEffectController] {gameObject.name}: Reset to start values");
        }
    }
    
    /// <summary>
    /// Gets the current radius from the radius animator
    /// </summary>
    public float GetCurrentRadius()
    {
        return radiusAnimator != null ? radiusAnimator.CurrentRadius : (spatialCollider != null ? spatialCollider.radius : 0f);
    }
    
    /// <summary>
    /// Gets the current material value from the material animator
    /// </summary>
    public float GetCurrentMaterialValue()
    {
        return materialAnimator != null ? materialAnimator.CurrentValue : 0f;
    }
    #endregion
    
    #region Unity Trigger Events
    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other == null) return;
        
        // Check if the colliding object is Player - start breach animation (only once)
        if (GameLayers.IsPlayer(other.gameObject.layer) && !hasBeenTriggered && playOnPlayerTrigger)
        {
            hasBeenTriggered = true;
            
            if (enableDebugLogging)
            {
                Debug.Log($"[BreachEffectController] {gameObject.name}: Player detected - starting breach animation");
            }
            
            PlayBreachEffect();
        }
        
        // Check if the colliding object is vegetation - trigger vegetation animation
        if (GameLayers.IsVegetation(other.gameObject.layer))
        {
            if (PoolManager.Instance.GetCachedComponent<MultiVegetationController>(other.gameObject, out MultiVegetationController vegetationController))
            {
                if (enableDebugLogging)
                {
                    Debug.Log($"[BreachEffectController] {gameObject.name}: Triggering vegetation animation on {other.gameObject.name}");
                }
                
                vegetationController.TriggerMaterialAnimation();
            }
            else if (enableDebugLogging)
            {
                Debug.LogWarning($"[BreachEffectController] {gameObject.name}: Vegetation object {other.gameObject.name} has no MultiVegetationController component");
            }
        }
    }
    #endregion
    
    #region ISpawnable Implementation
    public void OnSpawn()
    {
        // Reset trigger state when spawned from pool
        hasBeenTriggered = false;
        isPlaying = false;
        
        // Ensure radius is at original prefab value for trigger detection
        if (radiusAnimator != null)
        {
            radiusAnimator.ResetToOriginalRadius();
        }
        
        // Propagate spawn event to components
        if (enemySpawner != null)
        {
            enemySpawner.OnSpawn();
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEffectController] {gameObject.name}: Spawned from pool - reset trigger state and original radius");
        }
    }
    
    public void OnDespawn()
    {
        // Stop any running effects when returning to pool
        StopBreachEffect();
        
        // Reset radius to original prefab value (not start animation value)
        if (radiusAnimator != null)
        {
            radiusAnimator.ResetToOriginalRadius();
        }
        
        // Propagate despawn event to components
        if (enemySpawner != null)
        {
            enemySpawner.OnDespawn();
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEffectController] {gameObject.name}: Returning to pool - radius reset to original");
        }
    }
    #endregion
    
    #region Debug & Inspector
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    [ShowIf("@UnityEngine.Application.isPlaying")]
    private string runtimeStatus => $"Playing: {isPlaying} | Triggered: {hasBeenTriggered} | Radius: {GetCurrentRadius():F2} | Material: {GetCurrentMaterialValue():F2}";
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    [ShowIf("@UnityEngine.Application.isPlaying && enemySpawner != null")]
    private string enemySpawningStatus => $"Enemies: {enemySpawner.EnemiesSpawned}/{enemySpawner.MaxSpawnedEnemies} | Spawning: {enemySpawner.IsSpawning}";
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Component Status")]
    private string componentStatus => $"Material: {(materialAnimator != null ? "OK" : "Missing")} | Radius: {(radiusAnimator != null ? "OK" : "Missing")} | Spawner: {(enemySpawner != null ? "OK" : "Missing")}";
    
    #if UNITY_EDITOR
    [Title("Auto-Setup Components")]
    [Button("Add All Components", ButtonSizes.Medium)]
    private void AddAllComponents()
    {
        if (materialAnimator == null)
        {
            materialAnimator = gameObject.AddComponent<BreachMaterialAnimator>();
            Debug.Log("Added BreachMaterialAnimator");
        }
        
        if (radiusAnimator == null)
        {
            radiusAnimator = gameObject.AddComponent<BreachRadiusAnimator>();
            Debug.Log("Added BreachRadiusAnimator");
        }
        
        if (enemySpawner == null)
        {
            enemySpawner = gameObject.AddComponent<BreachEnemySpawner>();
            Debug.Log("Added BreachEnemySpawner");
        }
        
        Debug.Log($"BreachEffectController setup complete on {gameObject.name}");
    }
    
    [Button("Remove All Components", ButtonSizes.Medium)]
    private void RemoveAllComponents()
    {
        if (materialAnimator != null)
        {
            if (Application.isPlaying)
                Destroy(materialAnimator);
            else
                DestroyImmediate(materialAnimator);
            materialAnimator = null;
        }
        
        if (radiusAnimator != null)
        {
            if (Application.isPlaying)
                Destroy(radiusAnimator);
            else
                DestroyImmediate(radiusAnimator);
            radiusAnimator = null;
        }
        
        if (enemySpawner != null)
        {
            // Call OnDespawn() before destroying to ensure proper cleanup of spawned enemies
            if (Application.isPlaying)
            {
                enemySpawner.OnDespawn();
                Destroy(enemySpawner);
            }
            else
            {
                enemySpawner.OnDespawn();
                DestroyImmediate(enemySpawner);
            }
            enemySpawner = null;
        }
        
        Debug.Log($"Removed all breach effect components from {gameObject.name}");
    }
    
    [Button("Test All Effects", ButtonSizes.Large)]
    private void TestAllEffects()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test effects in play mode!");
            return;
        }
        
        PlayBreachEffect();
        Debug.Log("Testing all breach effects...");
    }
    #endif
    #endregion
}