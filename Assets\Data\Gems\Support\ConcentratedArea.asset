%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: ConcentratedArea
  m_EditorClassIdentifier: 
  icon: {fileID: 0}
  gemName: Concentrated Area
  description: Reduces area of effect but increases damage
  rarity: 0
  level: 1
  maxLevel: 20
  compatibleTags: 16
  damageIncreased: 0
  damageMore: 1.4
  cooldownMultiplier: 1
  manaCostMultiplier: 1.1
  skillDurationMultiplier: 1
  attackSpeedMultiplier: 1
  addedCritChance: 0
  critMultiplierModifier: 1
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 1
  addsMultipleProjectiles: 0
  extraProjectiles: 2
  projectileSpreadAngle: 15
  useParallelProjectiles: 0
  projectileLateralOffset: 0.6
  chainCount: 2
  areaIncreased: -30
  areaMore: 1
  addsFork: 0
  forkCount: 2
  igniteEffectivenessMultiplier: 1
  igniteDurationMultiplier: 1
  freezeEffectivenessMultiplier: 1
  freezeDurationMultiplier: 1
  bleedEffectivenessMultiplier: 1
  bleedDurationMultiplier: 1
  shockEffectivenessMultiplier: 1
  shockRangeMultiplier: 1
  forkAngle: 30
  addsSpellEcho: 0
  spellEchoDelay: 0.5
  spellEchoCount: 1
  spellEchoRadius: 2
  isConversionGem: 0
  conversionPercent: 50
  convertFromType: 0
  convertToType: 1
  physicalDamageIncreased: 0
  physicalDamageMore: 1
  fireDamageIncreased: 0
  fireDamageMore: 1
  iceDamageIncreased: 0
  iceDamageMore: 1
  lightningDamageIncreased: 0
  lightningDamageMore: 1
  elementalDamageIncreased: 0
  elementalDamageMore: 1
  isAutonomousGem: 0
  autonomousRange: 8
  autonomousTargetingMode: 0
  autonomousActivationDelay: 0.5
  autonomousMaxTargets: 3
  autonomousRequiresLineOfSight: 1
  autonomousPreferredTargetType: 0
