# LayerMask Migration Plan - Unity Native Layer System

## Overview

Migration from custom `CollisionLayers` enum to Unity's native LayerMask system for better performance, Unity integration, and simplified collision detection.

---

## Current State Analysis

### **CollisionLayers Enum Usage**
- **Total References**: 160+ occurrences across 33 files
- **Definition**: `Assets/src/Scripts/Systems/CollisionSystem/CollisionLayers.cs`
- **Pattern**: Flags enum with bitshift values (1 << 0, 1 << 1, etc.)

### **Current Usage Patterns**
```csharp
// ❌ Current: Custom enum with casting
gameObject.layer = (int)CollisionLayers.Enemy;
LayerMask enemyMask = (1 << (int)CollisionLayers.Enemy);
if (other.gameObject.layer == (int)CollisionLayers.Player) { }

// ✅ Target: Native Unity LayerMask
gameObject.layer = GameLayers.Enemy;
LayerMask enemyMask = GameLayers.EnemyMask;
if (other.gameObject.layer == GameLayers.Player) { }
```

---

## Migration Strategy

### **Phase 1: Unity Project Setup**

#### **1.1 Configure Unity Layers**
Set up layers in `Edit → Project Settings → Tags and Layers`:

```
Layer 3:  Player
Layer 4:  Enemy  
Layer 5:  PlayerProjectile
Layer 6:  EnemyProjectile
Layer 7:  Wall
Layer 8:  Trigger
Layer 9:  Pickup
Layer 10: Environment
Layer 11: Interactable
Layer 12: Vegetation
Layer 13: Breach
Layer 14: Shop
```

#### **1.2 Configure Physics2D Collision Matrix**
Set up collision rules in `Edit → Project Settings → Physics2D → Layer Collision Matrix`:

| Layer | Player | Enemy | PlayerProj | EnemyProj | Wall | Environment |
|-------|--------|-------|------------|-----------|------|-------------|
| Player | ❌ | ✅ | ❌ | ✅ | ✅ | ✅ |
| Enemy | ✅ | ❌ | ✅ | ❌ | ✅ | ✅ |
| PlayerProjectile | ❌ | ✅ | ❌ | ❌ | ✅ | ✅ |
| EnemyProjectile | ✅ | ❌ | ❌ | ❌ | ✅ | ✅ |

### **Phase 2: Create GameLayers Constants**

#### **2.1 Create GameLayers Class**
Replace `CollisionLayers.cs` with `GameLayers.cs`:

```csharp
using UnityEngine;

/// <summary>
/// Unity native layer constants and LayerMask definitions
/// Replaces the custom CollisionLayers enum system
/// </summary>
public static class GameLayers
{
    // Layer indices (matching Unity's TagManager)
    public const int Player = 3;
    public const int Enemy = 4;
    public const int PlayerProjectile = 5;
    public const int EnemyProjectile = 6;
    public const int Wall = 7;
    public const int Trigger = 8;
    public const int Pickup = 9;
    public const int Environment = 10;
    public const int Interactable = 11;
    public const int Vegetation = 12;
    public const int Breach = 13;
    public const int Shop = 14;
    
    // Pre-calculated LayerMasks for performance
    public static readonly LayerMask PlayerMask = 1 << Player;
    public static readonly LayerMask EnemyMask = 1 << Enemy;
    public static readonly LayerMask PlayerProjectileMask = 1 << PlayerProjectile;
    public static readonly LayerMask EnemyProjectileMask = 1 << EnemyProjectile;
    public static readonly LayerMask WallMask = 1 << Wall;
    public static readonly LayerMask TriggerMask = 1 << Trigger;
    public static readonly LayerMask PickupMask = 1 << Pickup;
    public static readonly LayerMask EnvironmentMask = 1 << Environment;
    public static readonly LayerMask InteractableMask = 1 << Interactable;
    public static readonly LayerMask VegetationMask = 1 << Vegetation;
    public static readonly LayerMask BreachMask = 1 << Breach;
    public static readonly LayerMask ShopMask = 1 << Shop;
    
    // Common layer combinations
    public static readonly LayerMask AllProjectilesMask = PlayerProjectileMask | EnemyProjectileMask;
    public static readonly LayerMask AllCharactersMask = PlayerMask | EnemyMask;
    public static readonly LayerMask AllSolidMask = WallMask | EnvironmentMask;
    public static readonly LayerMask DamageableByPlayerMask = EnemyMask | EnvironmentMask | VegetationMask;
    public static readonly LayerMask DamageableByEnemyMask = PlayerMask;
    
    // Helper methods for common operations
    public static bool IsPlayer(int layer) => layer == Player;
    public static bool IsEnemy(int layer) => layer == Enemy;
    public static bool IsPlayerProjectile(int layer) => layer == PlayerProjectile;
    public static bool IsEnemyProjectile(int layer) => layer == EnemyProjectile;
    
    /// <summary>
    /// Check if a layer is included in a LayerMask
    /// </summary>
    public static bool IsInLayerMask(int layer, LayerMask layerMask)
    {
        return (layerMask.value & (1 << layer)) != 0;
    }
}
```

### **Phase 3: Update SkillGemData**

#### **3.1 Migrate Property Types**
```csharp
// ❌ Before
public class SkillGemData : ScriptableObject
{
    [SerializeField] private CollisionLayers projectileLayer = CollisionLayers.PlayerProjectile;
    public CollisionLayers ProjectileLayer => projectileLayer;
}

// ✅ After  
public class SkillGemData : ScriptableObject
{
    [SerializeField] private int projectileLayer = GameLayers.PlayerProjectile;
    public int ProjectileLayer => projectileLayer;
    
    // Optional: Keep LayerMask property for direct use
    public LayerMask ProjectileLayerMask => 1 << projectileLayer;
}
```

### **Phase 4: Migration Patterns**

#### **4.1 Layer Assignment**
```csharp
// ❌ Before
gameObject.layer = (int)CollisionLayers.Enemy;
projectile.gameObject.layer = (int)skillData.projectileLayer;

// ✅ After
gameObject.layer = GameLayers.Enemy;  
projectile.gameObject.layer = skillData.ProjectileLayer;
```

#### **4.2 LayerMask Creation**
```csharp
// ❌ Before
LayerMask enemyMask = (1 << (int)CollisionLayers.Enemy);
LayerMask targetMask = (1 << (int)CollisionLayers.Enemy) | (1 << (int)CollisionLayers.Wall);

// ✅ After
LayerMask enemyMask = GameLayers.EnemyMask;
LayerMask targetMask = GameLayers.EnemyMask | GameLayers.WallMask;
```

#### **4.3 Layer Comparisons**
```csharp
// ❌ Before
if (other.gameObject.layer == (int)CollisionLayers.Player)
if (targetCollider.gameObject.layer == (int)CollisionLayers.Enemy)

// ✅ After
if (GameLayers.IsPlayer(other.gameObject.layer))
if (GameLayers.IsEnemy(targetCollider.gameObject.layer))
// Or direct comparison:
if (other.gameObject.layer == GameLayers.Player)
```

#### **4.4 Physics2D Queries**
```csharp
// ❌ Before  
LayerMask enemyMask = (1 << (int)CollisionLayers.Enemy);
int hitCount = Physics2D.OverlapCircleNonAlloc(position, radius, results, enemyMask);

// ✅ After
int hitCount = Physics2D.OverlapCircleNonAlloc(position, radius, results, GameLayers.EnemyMask);
```

---

## High-Impact Files Requiring Migration

### **Combat System** ⚔️
- **`Projectile.cs`** - 25+ references (layer assignment, target masks)
- **`BaseEnemy.cs`** - 8+ references (layer assignment, player detection)  
- **`MeleeAttackStrategy.cs`** - 10+ references (player detection, range checking)
- **`MagmaBall.cs`** - 15+ references (target detection, damage rules)

### **Skill System** ✨
- **`SkillGemData.cs`** - Property type changes required
- **`ProjectileSkillExecutor.cs`** - Projectile initialization logic
- **`SkillExecutor.cs`** - Skill execution and target detection

### **Interaction System** 🎯
- **`ChestComponent.cs`** - Interactable layer assignment
- **`InstantSpell.cs`** - Target layer logic
- **`OrbitingBlade.cs`** - Enemy detection masks

---

## Migration Benefits

### **Performance Improvements** ⚡
- **Pre-calculated LayerMasks**: No runtime bitshift operations
- **Unity Optimization**: Leverage Unity's native spatial partitioning
- **Reduced Casting**: No more `(int)CollisionLayers.X` operations
- **Better Caching**: Static readonly masks prevent repeated calculations

### **Developer Experience** 🛠️
- **Editor Integration**: Visual collision matrix in Physics2D settings
- **Type Safety**: Maintain compile-time layer checking with helper methods
- **Intellisense**: Better autocompletion with Unity's LayerMask
- **Debugging**: Unity Profiler shows native layer information

### **Unity Integration** 🎮
- **Physics2D Settings**: Visual collision matrix configuration
- **Inspector Support**: Native LayerMask fields in components
- **Scene View**: Layer visibility controls work seamlessly
- **Package Compatibility**: Better compatibility with Unity packages

---

## Implementation Timeline

### **Week 1: Foundation**
- ✅ Configure Unity layers in TagManager
- ✅ Set up Physics2D collision matrix
- ✅ Create `GameLayers.cs` constants class
- ✅ Update `SkillGemData` property types

### **Week 2: Core Systems**
- 🔄 Migrate `Projectile.cs` and combat system files
- 🔄 Update all attack strategy classes
- 🔄 Migrate enemy AI and detection logic

### **Week 3: Supporting Systems**
- 🔄 Update skill system and executors
- 🔄 Migrate interaction and pickup systems
- 🔄 Update editor tools and test systems

### **Week 4: Testing & Cleanup**
- 🔄 Remove `CollisionLayers.cs` and related files
- 🔄 Performance testing and optimization
- 🔄 Documentation updates

---

## Validation Checklist

- [ ] All Unity layers configured correctly (3-14)
- [ ] Physics2D collision matrix matches game rules
- [ ] `GameLayers.cs` constants created and tested
- [ ] All `(int)CollisionLayers.X` references eliminated
- [ ] Performance maintains 60 FPS target
- [ ] Zero-GC collision detection preserved
- [ ] All projectile systems working correctly
- [ ] Enemy AI detection functioning properly
- [ ] Pickup and interaction systems operational

This migration will significantly improve Unity integration while maintaining the zero-GC performance requirements of your combat system.