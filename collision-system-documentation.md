# Collision System Documentation - Unity 2D Rogue-like

## Overview

The collision system has been migrated from a custom SpatialCollider system to Unity Physics2D, implementing a hybrid approach that combines traditional Unity collision events with high-performance manual collision detection for zero-GC gameplay.

---

## Systems Using Unity Collision Events

### 1. **Pickup Systems** 🎯

#### **CurrencyPickup.cs**
- **Location**: `Assets/src/Scripts/Currency/CurrencyPickup.cs`
- **Events Used**: `OnTriggerEnter2D`
- **Collision Layer**: `CollisionLayers.Pickup`
- **Purpose**: Currency pickup with magnetic attraction to player
- **Implementation**: 
  - Trigger collider detects player layer interaction
  - Magnetic attraction with InBack easing animation
  - Satisfying pickup movement effects

#### **SplinterPickup.cs**
- **Location**: `Assets/src/Scripts/Pickup/SplinterPickup.cs`
- **Events Used**: `OnTriggerEnter2D`
- **Collision Layer**: `CollisionLayers.Pickup`
- **Purpose**: Splinter resource pickup with magnetic attraction
- **Implementation**: 
  - Identical pattern to CurrencyPickup
  - Trigger collider on pickup layer
  - Magnetic attraction with back-and-forth animation
  - Integrates with SplinterCollectionManager

### 2. **Interaction Systems** 🏪

#### **ShopTrigger.cs**
- **Location**: `Assets/src/Scripts/Shop/ShopTrigger.cs`
- **Events Used**: `OnTriggerEnter2D`, `OnTriggerExit2D`
- **Collision Layer**: `CollisionLayers.Shop`
- **Purpose**: Shop interaction system
- **Implementation**: 
  - Enter/exit range detection for shop interaction
  - Visual feedback (glow effects, interaction prompts)
  - Keyboard input handling for shop opening/closing

---

## Systems Using Alternative Collision Detection

### 1. **Projectile Systems** 🚀

#### **Projectile.cs** ⭐ *Primary Combat System*
- **Location**: `Assets/src/Scripts/Combat/Projectile.cs`
- **Method**: `Physics2D.CircleCast()` for moving collision detection
- **Performance Features**:
  - Pre-allocated arrays (`RaycastHit2D[] castResults = new RaycastHit2D[10]`)
  - Zero-GC collision detection with `CheckCollisionsDuringMovement()`
  - `ContactFilter2D` for layer-based filtering
- **Advanced Features**:
  - Custom `HitProcessingMode` enum (FirstHitOnly, AllHits, MaxHits)
  - Support for piercing, chaining, forking, and area damage
  - Integration with support gem effects
- **Why Custom**: Requires frame-perfect collision detection with zero allocations for 60 FPS performance

#### **SerpentineProjectile.cs**
- **Location**: `Assets/src/Scripts/Combat/SerpentineProjectile.cs`
- **Method**: ⚠️ **Uses Unity `OnTriggerEnter2D`** (NOT manual detection)
- **Purpose**: Serpentine movement patterns with collision handling
- **Note**: This is actually using traditional Unity collision events, not manual detection

### 2. **Enemy Attack Systems** ⚔️

#### **MeleeAttackStrategy.cs**
- **Location**: `Assets/src/Scripts/Enemy/Attack/MeleeAttackStrategy.cs`
- **Method**: `Physics2D.OverlapCircleNonAlloc()` for range detection
- **Performance Features**:
  - Pre-allocated array (`Collider2D[] overlapResults = new Collider2D[5]`)
  - `IsPlayerInMeleeRange()` with zero-GC overlap detection
  - Layer mask filtering for player detection
- **Purpose**: Validates player is in melee range before applying damage
- **Why Custom**: Needs frequent range checks without GC allocations

#### **ChargeAttackStrategy.cs**
- **Location**: `Assets/src/Scripts/Enemy/Attack/ChargeAttackStrategy.cs`
- **Method**: Manual distance calculation during charge movement
- **Performance Features**:
  - Uses `Vector3.sqrMagnitude` for performance (avoids sqrt calculations)
  - Continuous collision checking during charge coroutine
  - Custom damage radius with squared distance optimization
- **Purpose**: Dynamic collision detection during high-speed movement
- **Why Custom**: High-speed movement requires continuous collision checks

### 3. **Area Damage Systems** 💥

#### **AreaDamageSystem.cs**
- **Location**: `Assets/src/Scripts/Systems/Combat/AreaDamageSystem.cs`
- **Method**: `Physics2D.OverlapCircleNonAlloc()` for area effects
- **Performance Features**:
  - Pre-allocated array (`Collider2D[] areaResults = new Collider2D[50]`)
  - Zero-GC area damage detection
  - Multiple hit processing modes (FirstHitOnly, AllHits, MaxHits)
- **Advanced Features**:
  - Distance-based damage falloff using `AnimationCurve`
  - Custom radius and layer filtering
  - Damage scaling based on distance from center
- **Why Custom**: Area effects need efficient multi-target detection with falloff calculations

### 4. **Custom Spatial Systems** 🗺️ *(Legacy/Alternative)*

#### **CollisionManager.cs**
- **Location**: `Assets/src/Scripts/Systems/CollisionSystem/CollisionManager.cs`
- **Method**: Custom spatial hash grid implementation
- **Features**:
  - Chunk-based collision processing
  - Spatial partitioning for performance optimization
  - Custom collision event system (OnSpatialCollisionEnter, etc.)
  - Manual collision detection using geometric calculations
- **Purpose**: Alternative to Unity Physics2D with custom optimization

#### **SpatialHashGrid.cs**
- **Location**: `Assets/src/Scripts/Systems/CollisionSystem/SpatialHashGrid.cs`
- **Method**: Hash-based spatial partitioning
- **Features**:
  - `GetObjectsInRadius()` method for manual proximity queries
  - Zero-GC methods using pre-allocated collections
  - Custom collision shape support (Circle, Box)
- **Purpose**: Efficient spatial queries without Unity Physics2D

---

## Collision Detection Patterns Summary

| **System** | **Method** | **Performance Features** | **Use Case** | **Layer** |
|------------|------------|-------------------------|--------------|-----------|
| **CurrencyPickup** | `OnTriggerEnter2D` | Simple Unity events | Pickup interaction | Pickup |
| **SplinterPickup** | `OnTriggerEnter2D` | Simple Unity events | Resource collection | Pickup |
| **ShopTrigger** | `OnTriggerEnter2D/Exit2D` | Simple Unity events | Shop interaction | Shop |
| **Projectile** | `Physics2D.CircleCast` | Pre-allocated arrays, ContactFilter2D | Moving projectile collision | PlayerProjectile/EnemyProjectile |
| **MeleeAttackStrategy** | `Physics2D.OverlapCircleNonAlloc` | Zero-GC overlap detection | Range validation | Enemy |
| **ChargeAttackStrategy** | Manual distance calculation | Squared distance optimization | Dynamic movement collision | Enemy |
| **AreaDamageSystem** | `Physics2D.OverlapCircleNonAlloc` | Pre-allocated arrays, batch processing | Area effect damage | Multiple |

---

## Collision Layer Architecture

```csharp
public enum CollisionLayers
{
    Player = 1 << 0,           // Player character
    Enemy = 1 << 1,            // Enemy entities
    PlayerProjectile = 1 << 2, // Player-fired projectiles
    EnemyProjectile = 1 << 3,   // Enemy-fired projectiles
    Wall = 1 << 4,             // Static collision walls
    Trigger = 1 << 5,          // General trigger zones
    Pickup = 1 << 6,           // Collectible items
    Environment = 1 << 7,      // Environmental objects
    Interactable = 1 << 8,     // Interactive objects (chests, shops)
    Vegetation = 1 << 9,       // Destructible vegetation
    Breach = 1 << 10,          // Breach/corruption effects
    Shop = 1 << 11             // Shop interaction zones
}
```

---

## Design Philosophy

### **When to Use Unity Events vs Manual Detection**

#### **Use Unity Events (`OnTriggerEnter2D`) When:**
- ✅ Simple interaction detection (pickups, shop triggers)
- ✅ Low-frequency collision checks
- ✅ No complex collision processing needed
- ✅ Standard enter/exit behavior sufficient

#### **Use Manual Detection (`Physics2D.CircleCast/OverlapCircle`) When:**
- ⚡ High-performance requirements (zero-GC)
- ⚡ Complex collision processing (piercing, chaining, area damage)
- ⚡ Multiple hit detection needed
- ⚡ Custom collision shapes or behaviors
- ⚡ Frequent collision queries (every frame)

### **Performance Optimizations**

1. **Zero-GC Design**: Pre-allocated arrays for all collision queries
2. **Spatial Optimization**: Layer-based collision filtering
3. **Batch Processing**: Hit processing modes for different performance requirements
4. **Object Pooling**: All spawnable objects implement ISpawnable
5. **Contact Filtering**: `ContactFilter2D` for efficient layer masking

---

## Migration Notes

- **Before**: Custom SpatialCollider system with ISpatialCollisionHandler
- **After**: Hybrid Unity Physics2D + manual detection approach
- **Performance**: Maintained zero-GC requirements throughout migration
- **Compatibility**: All existing functionality preserved with Unity-native components

This collision system demonstrates a sophisticated approach that balances traditional Unity collision events for simple interactions with custom Physics2D systems for performance-critical combat mechanics.