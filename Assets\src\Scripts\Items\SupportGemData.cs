using UnityEngine;
using Sirenix.OdinInspector;

[CreateAssetMenu(fileName = "New Support Gem", menuName = "2D Rogue/Items/Support Gem")]
public class SupportGemData : GemData
{
    [Title("Support Properties")]
    [InfoBox("'Increased' modifiers are additive, 'More' modifiers are multiplicative")]
    
    [Title("Rarity Scaling")]
    [InfoBox("Define how base stats scale with gem rarity. 1.0 = no scaling")]
    [FoldoutGroup("Rarity Scaling", false)]
    [SerializeField] private bool useIntegerScaling = false;
    [FoldoutGroup("Rarity Scaling")]
    [InfoBox("Float scaling for percentage values")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float commonStatMultiplier = 0.7f;
    [FoldoutGroup("Rarity Scaling")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float uncommonStatMultiplier = 0.85f;
    [FoldoutGroup("Rarity Scaling")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float rareStatMultiplier = 1.0f;
    [FoldoutGroup("Rarity Scaling")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float epicStatMultiplier = 1.15f;
    [FoldoutGroup("Rarity Scaling")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float uniqueStatMultiplier = 1.3f;
    
    [FoldoutGroup("Rarity Scaling")]
    [InfoBox("Integer scaling for absolute values like projectiles/chains")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float commonIntMultiplier = 0.5f;
    [FoldoutGroup("Rarity Scaling")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float uncommonIntMultiplier = 0.75f;
    [FoldoutGroup("Rarity Scaling")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float rareIntMultiplier = 1.0f;
    [FoldoutGroup("Rarity Scaling")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float epicIntMultiplier = 1.5f;
    [FoldoutGroup("Rarity Scaling")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float uniqueIntMultiplier = 2.0f;
    
    [Title("Compatible Gem Tags")]
    [InfoBox("This support gem will only work with skill gems that have matching tags.")]
    [EnumToggleButtons]
    public GemTag compatibleTags = GemTag.Projectile;
    
    [Title("Damage Modifiers")]
    [Range(-50f, 200f)]
    [Tooltip("Additive damage increase percentage (stacks with other 'increased' modifiers)")]
    public float damageIncreased = 0f;
    
    [Range(0f, 2f)]
    [Tooltip("Multiplicative damage modifier (1.0 = no change, 1.3 = 30% more damage)")]
    public float damageMore = 1f;
    
    [Range(0f, 2f)]
    public float cooldownMultiplier = 1f;
    
    [Range(0f, 2f)]
    public float manaCostMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Skill duration multiplier for supported skills (1.0 = no change, 1.5 = 50% longer duration)")]
    public float skillDurationMultiplier = 1f;

    [Title("Combat Stats Modifiers")]
    [Range(0f, 2f)]
    [Tooltip("Attack speed multiplier for supported skills")]
    public float attackSpeedMultiplier = 1f;
    
    [Range(-50f, 50f)]
    [Tooltip("Added critical chance percentage")]
    public float addedCritChance = 0f;
    
    [Range(0f, 2f)]
    [Tooltip("Critical damage multiplier modifier")]
    public float critMultiplierModifier = 1f;
    
    [Title("Special Effects")]
    public bool addsPierce;
    public bool addsChain;
    public bool addsAreaDamage;
    public bool addsMultipleProjectiles;
    
    [ShowIf("addsMultipleProjectiles")]
    [Range(1, 10)]
    public int extraProjectiles = 2;
    
    [ShowIf("addsMultipleProjectiles")]
    [Range(5f, 45f)]
    [Tooltip("Angle spread between projectiles in degrees")]
    public float projectileSpreadAngle = 15f;
    
    [ShowIf("addsMultipleProjectiles")]
    [Tooltip("Use parallel projectiles instead of angular spread")]
    public bool useParallelProjectiles = false;
    
    [ShowIf("@addsMultipleProjectiles && useParallelProjectiles")]
    [Range(0.3f, 2f)]
    [Tooltip("Lateral spacing between parallel projectiles")]
    public float projectileLateralOffset = 0.6f;
    
    [ShowIf("@addsMultipleProjectiles && !useParallelProjectiles")]
    [Tooltip("Use random spread instead of even distribution within the spread angle")]
    public bool useRandomSpread = false;
    
    [ShowIf("addsChain")]
    [Range(1, 5)]
    public int chainCount = 2;
    
    [ShowIf("addsAreaDamage")]
    [Range(-50f, 200f)]
    [Tooltip("Increased area of effect percentage (e.g., 50 = 50% increased area)")]
    public float areaIncreased = 50f;

    [ShowIf("addsAreaDamage")]
    [Range(0f, 2f)]
    [Tooltip("More area of effect multiplier (1.0 = no change, 1.3 = 30% more area)")]
    public float areaMore = 1f;
    
    [Title("Fork Support")]
    [InfoBox("Projectiles split into multiple projectiles on impact")]
    public bool addsFork;

    [ShowIf("addsFork")]
    [Range(2, 5)]
    [Tooltip("Number of projectiles created when forking")]
    public int forkCount = 2;

    [Title("Status Effect Modifiers")]
    [InfoBox("These modifiers enhance status effects applied by supported skills")]

    [Range(0f, 2f)]
    [Tooltip("Multiplier for ignite damage (1.0 = no change, 1.5 = 50% more damage)")]
    public float igniteEffectivenessMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for ignite duration (1.0 = no change, 1.3 = 30% longer)")]
    public float igniteDurationMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for freeze effectiveness (1.0 = no change, 1.2 = 20% stronger slow)")]
    public float freezeEffectivenessMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for freeze duration (1.0 = no change, 1.5 = 50% longer)")]
    public float freezeDurationMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for bleed damage (1.0 = no change, 1.4 = 40% more damage)")]
    public float bleedEffectivenessMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for bleed duration (1.0 = no change, 1.3 = 30% longer)")]
    public float bleedDurationMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for shock chain damage (1.0 = no change, 1.5 = 50% more damage)")]
    public float shockEffectivenessMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for shock chain range (1.0 = no change, 1.3 = 30% more range)")]
    public float shockRangeMultiplier = 1f;
    
    [ShowIf("addsFork")]
    [Range(10f, 60f)]
    [Tooltip("Angle spread between forked projectiles in degrees")]
    public float forkAngle = 30f;
    
    [Title("Spell Echo")]
    [InfoBox("Automatically recasts supported spells after a delay")]
    public bool addsSpellEcho;
    
    [ShowIf("addsSpellEcho")]
    [Range(0.1f, 2f)]
    [Tooltip("Delay in seconds before the spell echoes")]
    public float echoDelay = 0.4f;
    
    [ShowIf("addsSpellEcho")]
    [Range(1, 5)]
    [Tooltip("Number of times the spell will echo")]
    public int echoCount = 1;
    
    [ShowIf("addsSpellEcho")]
    [Range(0f, 3f)]
    [Tooltip("Random position offset for echo casts (0 = exact same position)")]
    public float echoSpreadRadius = 0f;

    [Title("Autonomous Casting")]
    [InfoBox("Makes supported skills automatically target and cast at nearby enemies")]
    public bool addsAutonomous;

    [ShowIf("addsAutonomous")]
    [Range(3f, 15f)]
    [Tooltip("Maximum range for autonomous targeting")]
    public float autonomousRange = 8f;

    [ShowIf("addsAutonomous")]
    [Range(0.1f, 2f)]
    [Tooltip("How often to check for targets (in seconds)")]
    public float autonomousUpdateInterval = 0.5f;

    [Title("Damage Conversion")]
    [InfoBox("⚠ RESTRICTION: Only one conversion gem can be equipped per skill. Equipping a new conversion gem will automatically unequip the previous one.")]
    public bool isConversionGem = false;

    [ShowIf("isConversionGem")]
    [Range(0f, 100f)]
    [Tooltip("Percentage of damage to convert")]
    public float conversionPercent = 50f;

    [ShowIf("isConversionGem")]
    [Tooltip("Damage type to convert from")]
    public DamageType convertFromType = DamageType.Physical;

    [ShowIf("isConversionGem")]
    [Tooltip("Damage type to convert to")]
    public DamageType convertToType = DamageType.Fire;

    [Title("Type-Specific Damage Modifiers")]
    [FoldoutGroup("Physical Damage")]
    [Range(-50f, 200f)]
    [Tooltip("Increased physical damage percentage")]
    public float physicalDamageIncreased = 0f;
    
    [FoldoutGroup("Physical Damage")]
    [Range(0f, 2f)]
    [Tooltip("More physical damage multiplier")]
    public float physicalDamageMore = 1f;

    [FoldoutGroup("Fire Damage")]
    [Range(-50f, 200f)]
    [Tooltip("Increased fire damage percentage")]
    public float fireDamageIncreased = 0f;
    
    [FoldoutGroup("Fire Damage")]
    [Range(0f, 2f)]
    [Tooltip("More fire damage multiplier")]
    public float fireDamageMore = 1f;

    [FoldoutGroup("Ice Damage")]
    [Range(-50f, 200f)]
    [Tooltip("Increased ice damage percentage")]
    public float iceDamageIncreased = 0f;
    
    [FoldoutGroup("Ice Damage")]
    [Range(0f, 2f)]
    [Tooltip("More ice damage multiplier")]
    public float iceDamageMore = 1f;

    [FoldoutGroup("Lightning Damage")]
    [Range(-50f, 200f)]
    [Tooltip("Increased lightning damage percentage")]
    public float lightningDamageIncreased = 0f;
    
    [FoldoutGroup("Lightning Damage")]
    [Range(0f, 2f)]
    [Tooltip("More lightning damage multiplier")]
    public float lightningDamageMore = 1f;

    [FoldoutGroup("Elemental Damage")]
    [InfoBox("Applies to Fire, Ice, and Lightning damage")]
    [Range(-50f, 200f)]
    [Tooltip("Increased elemental damage percentage")]
    public float elementalDamageIncreased = 0f;
    
    [FoldoutGroup("Elemental Damage")]
    [Range(0f, 2f)]
    [Tooltip("More elemental damage multiplier")]
    public float elementalDamageMore = 1f;

    [Title("Penetration Stats")]
    [InfoBox("Penetration mechanics for bypassing enemy defenses following Path of Exile mechanics")]
    
    [FoldoutGroup("Armor Penetration")]
    [Range(0f, 75f)]
    [Tooltip("Percentage of armor that is ignored (0-75%). Higher values bypass more armor.")]
    public float armorPenetrationPercent = 0f;
    
    [FoldoutGroup("Armor Penetration")]
    [Range(0, 500)]
    [Tooltip("Flat armor rating reduction before percentage calculations.")]
    public int flatArmorPenetration = 0;
    
    [FoldoutGroup("Elemental Penetration")]
    [Range(0f, 50f)]
    [Tooltip("Fire resistance penetration. Reduces enemy fire resistance by this amount.")]
    public float firePenetration = 0f;
    
    [FoldoutGroup("Elemental Penetration")]
    [Range(0f, 50f)]
    [Tooltip("Cold resistance penetration. Reduces enemy cold resistance by this amount.")]
    public float coldPenetration = 0f;
    
    [FoldoutGroup("Elemental Penetration")]
    [Range(0f, 50f)]
    [Tooltip("Lightning resistance penetration. Reduces enemy lightning resistance by this amount.")]
    public float lightningPenetration = 0f;
    
    [FoldoutGroup("Elemental Penetration")]
    [Range(0f, 25f)]
    [Tooltip("Elemental penetration. Applies to all elemental damage types (Fire, Cold, Lightning).")]
    public float elementalPenetration = 0f;

    [Title("Special Damage Mechanics")]
    [InfoBox("Advanced damage mechanics for specialized support gems")]

    [Tooltip("Prevents all elemental damage output (Fire, Ice, Lightning) - used by Brutality Support")]
    public bool preventsElementalDamage = false;

    [Tooltip("Provides damage bonus on every Nth hit/cast - used by Ruthless Support")]
    public bool addsRuthlessHits = false;

    [ShowIf("addsRuthlessHits")]
    [Range(2, 10)]
    [Tooltip("Every Nth hit gets the damage bonus (e.g., 3 = every third hit)")]
    public int ruthlessHitInterval = 3;

    [ShowIf("addsRuthlessHits")]
    [Range(0f, 5f)]
    [Tooltip("Damage multiplier for ruthless hits (1.0 = no bonus, 2.0 = 100% more damage)")]
    public float ruthlessDamageMultiplier = 2f;

    public override string GetTooltipText()
    {
        var tooltip = $"<color=#{ColorUtility.ToHtmlStringRGB(GetRarityColor())}>{gemName}</color>\n" +
             $"{rarity} Support Gem\n";
             
        // Add compatible tags display
        if (compatibleTags != GemTag.None)
        {
            var tagNames = new System.Collections.Generic.List<string>();
            if ((compatibleTags & GemTag.Melee) != 0) tagNames.Add(GemTag.Melee.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Projectile) != 0) tagNames.Add(GemTag.Projectile.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Spell) != 0) tagNames.Add(GemTag.Spell.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Duration) != 0) tagNames.Add(GemTag.Duration.GetColoredDisplayName());
            if ((compatibleTags & GemTag.AreaOfEffect) != 0) tagNames.Add(GemTag.AreaOfEffect.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Ailment) != 0) tagNames.Add(GemTag.Ailment.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Physical) != 0) tagNames.Add(GemTag.Physical.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Fire) != 0) tagNames.Add(GemTag.Fire.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Cold) != 0) tagNames.Add(GemTag.Cold.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Lightning) != 0) tagNames.Add(GemTag.Lightning.GetColoredDisplayName());
            tooltip += $"Compatible with: {string.Join(", ", tagNames)}\n";
        }
        
        tooltip += $"\n{description}\n\n";
        
        // Use consolidated damage modifier formatting to avoid duplicate display
        string damageModifiers = TooltipFormatter.FormatSupportGemDamageModifiers(damageIncreased, damageMore);
        if (!string.IsNullOrEmpty(damageModifiers))
        {
            tooltip += damageModifiers + "\n";
        }

        if (cooldownMultiplier != 1f)
        tooltip += TooltipFormatter.FormatStat("Cooldown", $"{(cooldownMultiplier - 1f) * 100:+0;-0}%") + "\n";

        if (manaCostMultiplier != 1f)
        tooltip += TooltipFormatter.FormatStat("Mana Cost", $"{(manaCostMultiplier - 1f) * 100:+0;-0}%") + "\n";

        if (skillDurationMultiplier != 1f)
        tooltip += TooltipFormatter.FormatStat("Duration", $"{(skillDurationMultiplier - 1f) * 100:+0;-0}%") + "\n";

        if (attackSpeedMultiplier != 1f)
        tooltip += TooltipFormatter.FormatStat("Attack Speed", $"{(attackSpeedMultiplier - 1f) * 100:+0;-0}%") + "\n";

        if (addedCritChance != 0f)
        tooltip += TooltipFormatter.FormatStat("Critical Chance", $"{addedCritChance:+0;-0}%") + "\n";

        if (critMultiplierModifier != 1f)
        tooltip += TooltipFormatter.FormatStat("Critical Multiplier", $"{(critMultiplierModifier - 1f) * 100:+0;-0}%") + "\n";
        
        if (addsPierce)
        tooltip += "Adds Piercing\n";

        if (addsChain)
        tooltip += $"Chains {chainCount} times\n";

        if (addsFork)
        tooltip += $"Forks into {forkCount} projectiles (Spread: {forkAngle}°)\n";

        if (addsAreaDamage)
        {
            if (areaIncreased != 0f)
                tooltip += TooltipFormatter.FormatStat("Area of Effect", $"{areaIncreased:+0;-0}% increased") + "\n";
            if (areaMore != 1f)
                tooltip += TooltipFormatter.FormatStat("Area of Effect", $"{(areaMore - 1f) * 100:+0;-0}% more") + "\n";
        }

        if (addsMultipleProjectiles)
        {
            if (useParallelProjectiles)
                tooltip += $"+{extraProjectiles} Projectiles (Parallel, {projectileLateralOffset:F1}u spacing)\n";
            else
                tooltip += $"+{extraProjectiles} Projectiles (Spread: {projectileSpreadAngle}°)\n";
        }

        if (addsSpellEcho)
        {
            tooltip += $"Spell Echo: Recasts {echoCount} time{(echoCount > 1 ? "s" : "")} after {echoDelay}s\n";
            if (echoSpreadRadius > 0)
                tooltip += $"Echo Spread Radius: {echoSpreadRadius}\n";
        }

        if (addsAutonomous)
        {
            tooltip += $"<color={TooltipFormatter.HEADER_COLOR}>Autonomous Casting</color>\n";
            tooltip += TooltipFormatter.FormatStat("Range", $"{autonomousRange:F1}") + "\n";
            tooltip += $"Automatically targets enemies within range\n";
        }

        if (preventsElementalDamage)
        {
            tooltip += $"\n<color={TooltipFormatter.HEADER_COLOR}>Special Mechanics</color>\n";
            tooltip += $"<color=#FF6B6B>Prevents all elemental damage</color>\n";
        }

        if (addsRuthlessHits)
        {
            if (!preventsElementalDamage)
                tooltip += $"\n<color={TooltipFormatter.HEADER_COLOR}>Special Mechanics</color>\n";
            tooltip += TooltipFormatter.FormatStat("Ruthless Hits", $"Every {ruthlessHitInterval}{GetOrdinalSuffix(ruthlessHitInterval)} hit") + "\n";
            tooltip += TooltipFormatter.FormatStat("Ruthless Damage", $"{(ruthlessDamageMultiplier - 1f) * 100:F0}% more") + "\n";
        }

        if (isConversionGem)
        {
            tooltip += $"\n<color={TooltipFormatter.HEADER_COLOR}>Damage Conversion</color>\n";
            tooltip += TooltipFormatter.FormatStat("Converts", $"{conversionPercent:F0}% {convertFromType} to {convertToType}") + "\n";
            tooltip += $"<color=#FF9500>⚠ Only one conversion gem per skill</color>\n";
        }

        // Add type-specific damage modifiers
        bool hasTypeDamageModifiers = physicalDamageIncreased != 0f || physicalDamageMore != 1f ||
                                     fireDamageIncreased != 0f || fireDamageMore != 1f ||
                                     iceDamageIncreased != 0f || iceDamageMore != 1f ||
                                     lightningDamageIncreased != 0f || lightningDamageMore != 1f ||
                                     elementalDamageIncreased != 0f || elementalDamageMore != 1f;

        if (hasTypeDamageModifiers)
        {
            tooltip += $"\n<color={TooltipFormatter.HEADER_COLOR}>Type Damage Modifiers:</color>\n";

            // Physical
            if (physicalDamageIncreased != 0f)
                tooltip += TooltipFormatter.FormatStat("• Physical Damage", $"{physicalDamageIncreased:+0;-0}% increased") + "\n";
            if (physicalDamageMore != 1f)
                tooltip += TooltipFormatter.FormatStat("• Physical Damage", $"{(physicalDamageMore - 1f) * 100:+0;-0}% more") + "\n";

            // Fire
            if (fireDamageIncreased != 0f)
                tooltip += TooltipFormatter.FormatStat("• Fire Damage", $"{fireDamageIncreased:+0;-0}% increased") + "\n";
            if (fireDamageMore != 1f)
                tooltip += TooltipFormatter.FormatStat("• Fire Damage", $"{(fireDamageMore - 1f) * 100:+0;-0}% more") + "\n";

            // Ice
            if (iceDamageIncreased != 0f)
                tooltip += TooltipFormatter.FormatStat("• Ice Damage", $"{iceDamageIncreased:+0;-0}% increased") + "\n";
            if (iceDamageMore != 1f)
                tooltip += TooltipFormatter.FormatStat("• Ice Damage", $"{(iceDamageMore - 1f) * 100:+0;-0}% more") + "\n";

            // Lightning
            if (lightningDamageIncreased != 0f)
                tooltip += TooltipFormatter.FormatStat("• Lightning Damage", $"{lightningDamageIncreased:+0;-0}% increased") + "\n";
            if (lightningDamageMore != 1f)
                tooltip += TooltipFormatter.FormatStat("• Lightning Damage", $"{(lightningDamageMore - 1f) * 100:+0;-0}% more") + "\n";

            // Elemental
            if (elementalDamageIncreased != 0f)
                tooltip += TooltipFormatter.FormatStat("• Elemental Damage", $"{elementalDamageIncreased:+0;-0}% increased") + "\n";
            if (elementalDamageMore != 1f)
                tooltip += TooltipFormatter.FormatStat("• Elemental Damage", $"{(elementalDamageMore - 1f) * 100:+0;-0}% more") + "\n";
        }

        // Add penetration stats if any are present (Path of Exile style)
        if (HasPenetrationStats())
        {
            tooltip += $"\n<color={TooltipFormatter.HEADER_COLOR}>Penetration:</color>\n";

            // Path of Exile style descriptions
            if (armorPenetrationPercent > 0f)
                tooltip += $"<color={TooltipFormatter.PENETRATION_COLOR}>Supported Skills ignore {armorPenetrationPercent:F0}% of Enemy Armor</color>\n";
            if (flatArmorPenetration > 0)
                tooltip += $"<color={TooltipFormatter.PENETRATION_COLOR}>Supported Skills reduce Enemy Armor by {flatArmorPenetration}</color>\n";
            if (firePenetration > 0f)
                tooltip += $"<color={TooltipFormatter.IGNITE_COLOR}>Supported Skills penetrate {firePenetration:F0}% Fire Resistance</color>\n";
            if (coldPenetration > 0f)
                tooltip += $"<color={TooltipFormatter.FREEZE_COLOR}>Supported Skills penetrate {coldPenetration:F0}% Cold Resistance</color>\n";
            if (lightningPenetration > 0f)
                tooltip += $"<color={TooltipFormatter.SHOCK_COLOR}>Supported Skills penetrate {lightningPenetration:F0}% Lightning Resistance</color>\n";
            if (elementalPenetration > 0f)
                tooltip += $"<color={TooltipFormatter.PENETRATION_COLOR}>Supported Skills penetrate {elementalPenetration:F0}% of all Elemental Resistances</color>\n";
        }

        // Add status effect modifiers if any are present
        bool hasStatusEffectModifiers = igniteEffectivenessMultiplier != 1f || igniteDurationMultiplier != 1f ||
                                       freezeEffectivenessMultiplier != 1f || freezeDurationMultiplier != 1f ||
                                       bleedEffectivenessMultiplier != 1f || bleedDurationMultiplier != 1f ||
                                       shockEffectivenessMultiplier != 1f || shockRangeMultiplier != 1f;

        if (hasStatusEffectModifiers)
        {
            tooltip += $"\n<color={TooltipFormatter.HEADER_COLOR}>Status Effect Modifiers:</color>\n";

            // Ignite modifiers
            if (igniteEffectivenessMultiplier != 1f)
                tooltip += TooltipFormatter.FormatPercentageModifier("• Ignite Damage", igniteEffectivenessMultiplier) + "\n";
            if (igniteDurationMultiplier != 1f)
                tooltip += TooltipFormatter.FormatPercentageModifier("• Ignite Duration", igniteDurationMultiplier) + "\n";

            // Freeze modifiers
            if (freezeEffectivenessMultiplier != 1f)
                tooltip += TooltipFormatter.FormatPercentageModifier("• Freeze Effectiveness", freezeEffectivenessMultiplier) + "\n";
            if (freezeDurationMultiplier != 1f)
                tooltip += TooltipFormatter.FormatPercentageModifier("• Freeze Duration", freezeDurationMultiplier) + "\n";

            // Bleed modifiers
            if (bleedEffectivenessMultiplier != 1f)
                tooltip += TooltipFormatter.FormatPercentageModifier("• Bleed Damage", bleedEffectivenessMultiplier) + "\n";
            if (bleedDurationMultiplier != 1f)
                tooltip += TooltipFormatter.FormatPercentageModifier("• Bleed Duration", bleedDurationMultiplier) + "\n";

            // Shock modifiers
            if (shockEffectivenessMultiplier != 1f)
                tooltip += TooltipFormatter.FormatPercentageModifier("• Shock Damage", shockEffectivenessMultiplier) + "\n";
            if (shockRangeMultiplier != 1f)
                tooltip += TooltipFormatter.FormatPercentageModifier("• Shock Range", shockRangeMultiplier) + "\n";
        }

        return tooltip;
    }

    /// <summary>
    /// Apply this support gem's status effect modifiers to a base configuration
    /// </summary>
    /// <param name="baseConfig">Base status effect configuration to modify</param>
    /// <returns>Modified configuration with support gem bonuses applied</returns>
    public StatusEffectHelper.StatusEffectConfig ApplyStatusEffectModifiers(StatusEffectHelper.StatusEffectConfig baseConfig)
    {
        var modifiedConfig = baseConfig;

        // Apply ignite modifiers
        modifiedConfig.ignitePercent *= igniteEffectivenessMultiplier;
        modifiedConfig.igniteDuration *= igniteDurationMultiplier;

        // Apply freeze modifiers
        modifiedConfig.freezeSlowAmount = Mathf.Min(1f, modifiedConfig.freezeSlowAmount * freezeEffectivenessMultiplier);
        modifiedConfig.freezeDuration *= freezeDurationMultiplier;

        // Apply bleed modifiers
        modifiedConfig.bleedPercent *= bleedEffectivenessMultiplier;
        modifiedConfig.bleedDuration *= bleedDurationMultiplier;

        // Apply shock modifiers
        modifiedConfig.shockChainDamage *= shockEffectivenessMultiplier;
        modifiedConfig.shockChainRange *= shockRangeMultiplier;

        return modifiedConfig;
    }

    /// <summary>
    /// Get the rarity multiplier for scaling base stats
    /// </summary>
    public float GetRarityMultiplier(GemRarity gemRarity)
    {
        if (useIntegerScaling)
        {
            return gemRarity switch
            {
                GemRarity.Common => commonIntMultiplier,
                GemRarity.Uncommon => uncommonIntMultiplier,
                GemRarity.Rare => rareIntMultiplier,
                GemRarity.Epic => epicIntMultiplier,
                GemRarity.Unique => uniqueIntMultiplier,
                _ => 1f
            };
        }
        else
        {
            return gemRarity switch
            {
                GemRarity.Common => commonStatMultiplier,
                GemRarity.Uncommon => uncommonStatMultiplier,
                GemRarity.Rare => rareStatMultiplier,
                GemRarity.Epic => epicStatMultiplier,
                GemRarity.Unique => uniqueStatMultiplier,
                _ => 1f
            };
        }
    }
    
    /// <summary>
    /// Check if this support gem uses integer scaling
    /// </summary>
    public bool UsesIntegerScaling => useIntegerScaling;

    /// <summary>
    /// Check if this support gem has any penetration stats
    /// </summary>
    public bool HasPenetrationStats()
    {
        return armorPenetrationPercent > 0f || flatArmorPenetration > 0 ||
               firePenetration > 0f || coldPenetration > 0f || lightningPenetration > 0f ||
               elementalPenetration > 0f;
    }

    /// <summary>
    /// Get penetration stats scaled by gem rarity
    /// </summary>
    public (float armorPenetrationPercent, int flatArmorPenetration, float firePenetration, 
            float coldPenetration, float lightningPenetration, float elementalPenetration) 
            GetScaledPenetrationStats(GemRarity rarity)
    {
        float rarityMultiplier = GetRarityMultiplier(rarity);
        
        // Use asymmetric scaling - penetration is always a benefit, so it always scales up
        return (
            armorPenetrationPercent * rarityMultiplier,
            useIntegerScaling ? Mathf.FloorToInt(flatArmorPenetration * rarityMultiplier) : flatArmorPenetration,
            firePenetration * rarityMultiplier,
            coldPenetration * rarityMultiplier,
            lightningPenetration * rarityMultiplier,
            elementalPenetration * rarityMultiplier
        );
    }

    /// <summary>
    /// Get ordinal suffix for numbers (1st, 2nd, 3rd, 4th, etc.)
    /// </summary>
    private string GetOrdinalSuffix(int number)
    {
        if (number <= 0) return "";

        int lastDigit = number % 10;
        int lastTwoDigits = number % 100;

        // Special cases for 11th, 12th, 13th
        if (lastTwoDigits >= 11 && lastTwoDigits <= 13)
            return "th";

        return lastDigit switch
        {
            1 => "st",
            2 => "nd",
            3 => "rd",
            _ => "th"
        };
    }
}