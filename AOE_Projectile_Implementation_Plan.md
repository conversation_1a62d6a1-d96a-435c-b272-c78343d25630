# AOE Projectile Implementation Plan

## Overview

This plan outlines the implementation of Area of Effect (AOE) projectiles for the Unity 2D rogue-like project, building upon the existing projectile, collision, damage, and visual effects systems. The implementation will follow the project's architectural principles (SOLID, DRY, KISS, YAGNI) while maintaining zero-GC gameplay requirements.

## Feature Goals

- **Primary Goal**: Add projectiles that deal both direct impact damage and area-of-effect damage
- **Secondary Goals**: 
  - Integrate seamlessly with existing support gem system
  - Maintain 60 FPS performance with zero garbage collection
  - Provide satisfying visual and audio feedback
  - Support Path of Exile-style AOE mechanics (damage falloff, multiple hit modes)

## Current System Analysis

### Strengths
- **Robust Foundation**: Existing `Projectile.cs` already supports area damage via support gems
- **Performance Optimized**: Zero-GC patterns implemented throughout (pre-allocated arrays, object pooling)
- **Complete Integration**: Full support for damage breakdown, critical strikes, status effects
- **Visual Effects Ready**: ParticleEffectManager and AreaDamageSystem provide AOE foundation
- **Gem System Compatible**: Support gems automatically modify area radius and damage

### Integration Points
- **AreaDamageSystem**: Provides distance-based damage falloff and hit processing
- **SupportGemProcessor**: Handles area radius/damage modifications from support gems  
- **ParticleEffectManager**: Manages explosion visual effects
- **DamageCalculator**: Preserves damage type breakdown for proper gem interactions
- **PoolManager**: Manages lifecycle of AOE effect objects

## Technical Requirements

### Performance Constraints
- **Zero-GC Gameplay**: No allocations during Update/FixedUpdate loops
- **60 FPS Target**: Maximum 16ms frame time budget
- **Memory Efficient**: Use object pooling for all runtime objects
- **Optimized Collision**: Leverage existing pre-allocated arrays for area queries

### Architecture Requirements
- **Follow Existing Patterns**: Inherit from `Projectile` class, implement `ISpawnable`
- **Support Gem Integration**: Preserve damage breakdown and gem effect application
- **Layer-Based Targeting**: Use `GameLayers` for precise collision detection
- **Component Caching**: Leverage `PoolManager.GetCachedComponent<>()` for performance

## Implementation Plan

### Phase 1: Core AOE Projectile Component

#### 1.1 Create AOEProjectile Class
**File**: `H:\UnityProjects\Projects\2DRogue\Assets\src\Scripts\Combat\AOEProjectile.cs`

```csharp
public class AOEProjectile : Projectile
{
    [Header("AOE Configuration")]
    [SerializeField] private bool triggerAOEOnDirectHit = true;
    [SerializeField] private bool triggerAOEOnLifetimeEnd = false;
    [SerializeField] private float aoeRadius = 3.0f;
    [SerializeField] private float aoeDamageMultiplier = 0.7f;
    [SerializeField] private AnimationCurve aoeDamageFalloff = AnimationCurve.Linear(0, 1, 1, 0.3f);
    
    [Header("AOE Visual Effects")]
    [SerializeField] private ParticleType explosionParticleType = ParticleType.SparkImpact;
    [SerializeField] private int explosionParticleCount = 25;
    [SerializeField] private GameObject aoeWarningPrefab; // Optional warning indicator
    
    private AreaDamageSystem aoeSystem;
    
    // Pre-allocated for zero-GC
    private static readonly Collider2D[] aoeResults = new Collider2D[64];
}
```

**Key Features**:
- Inherits all existing projectile functionality (pierce, chain, fork, status effects)
- Configurable AOE trigger conditions (on hit, on timeout, both)
- Support gem integration through base class damage system
- Pre-allocated collision arrays for zero-GC performance

#### 1.2 AOE Damage Processing
**Implementation Strategy**:

```csharp
protected override void ProcessHit(RaycastHit2D hit)
{
    Vector3 impactPoint = hit.point;
    
    // Apply direct hit damage (existing system)
    base.ProcessHit(hit);
    
    // Trigger AOE damage if configured
    if (triggerAOEOnDirectHit && hasAreaDamage)
    {
        TriggerAOEExplosion(impactPoint);
    }
}

private void TriggerAOEExplosion(Vector3 center)
{
    // Calculate AOE damage using existing damage breakdown
    DamageBreakdown aoeDamage = damageBreakdown.ScaleAllDamage(aoeDamageMultiplier);
    
    // Apply area damage using existing system
    aoeSystem.ApplyAreaDamage(
        center, 
        GetEffectiveAOERadius(), 
        aoeDamage, 
        GetTargetLayerMask(),
        aoeDamageFalloff
    );
    
    // Spawn explosion visual effects
    SpawnExplosionEffects(center);
    
    // Apply blood splatters for visual feedback
    ApplyBloodEffects(center);
}
```

### Phase 2: Support Gem Integration

#### 2.1 Extend Existing Support Gems
**Leverage Existing System**: Current support gems already provide area damage modifiers:
- **Area of Effect Support**: `addsAreaDamage: 1`, `areaRadius: 5`
- **Concentrated Area Support**: `areaRadius: 2.5`, `areaRadiusFactor: 0.5`, `moreAreaDamage: 1.4`

**Enhanced Integration**:

```csharp
private float GetEffectiveAOERadius()
{
    float baseRadius = aoeRadius;
    
    // Apply support gem modifiers (existing system)
    foreach (var gem in supportGems)
    {
        if (gem.gemData.areaRadiusFactor != 0)
            baseRadius *= gem.gemData.areaRadiusFactor;
        if (gem.gemData.areaRadius != 0)
            baseRadius += gem.gemData.areaRadius;
    }
    
    return baseRadius;
}
```

#### 2.2 New AOE-Specific Support Gems (Optional)
**File**: Update existing support gem ScriptableObjects

Consider adding specialized AOE support gems:
- **Concentrated Blast Support**: Smaller radius, higher damage
- **Wide Area Support**: Larger radius, reduced damage
- **Penetrating Blast Support**: AOE ignores a percentage of enemy resistance

### Phase 3: Visual Effects System

#### 3.1 Explosion Effect Integration
**Strategy**: Extend existing ParticleEffectManager patterns

```csharp
private void SpawnExplosionEffects(Vector3 center)
{
    // Main explosion particles
    ParticleEffectManager.Instance?.SpawnParticle(
        explosionParticleType, 
        center, 
        explosionParticleCount
    );
    
    // Scale particle count based on AOE radius
    int scaledParticleCount = Mathf.RoundToInt(
        explosionParticleCount * (GetEffectiveAOERadius() / aoeRadius)
    );
    
    // Additional ring particles for area indication
    SpawnRingParticles(center, GetEffectiveAOERadius(), scaledParticleCount);
}

private void SpawnRingParticles(Vector3 center, float radius, int count)
{
    for (int i = 0; i < count; i++)
    {
        float angle = (i / (float)count) * 2f * Mathf.PI;
        Vector3 ringPosition = center + new Vector3(
            Mathf.Cos(angle) * radius * 0.8f,
            Mathf.Sin(angle) * radius * 0.8f,
            0
        );
        
        ParticleEffectManager.Instance?.SpawnParticle(
            ParticleType.SparkImpact,
            ringPosition,
            2
        );
    }
}
```

#### 3.2 Blood Splatter Integration
**Strategy**: Leverage existing ComputeBufferBloodSplatterSystem

```csharp
private void ApplyBloodEffects(Vector3 center)
{
    // Query for damaged enemies in AOE radius
    int hits = Physics2D.OverlapCircleNonAlloc(
        center, 
        GetEffectiveAOERadius(), 
        aoeResults, 
        GameLayers.EnemyMask
    );
    
    // Spawn blood splatters for each enemy hit
    for (int i = 0; i < hits; i++)
    {
        Vector3 enemyPosition = aoeResults[i].transform.position;
        Vector3 direction = (enemyPosition - center).normalized;
        
        BloodSplatterManager.Instance?.SpawnHitSplatter(
            enemyPosition,
            direction * 2f, // Velocity away from explosion center
            BloodSplatterManager.SplatterType.Hit
        );
    }
}
```

### Phase 4: Skill Executor Implementation

#### 4.1 Create AOEProjectileSkillExecutor
**File**: `H:\UnityProjects\Projects\2DRogue\Assets\src\Scripts\Systems\Skills\Executors\AOEProjectileSkillExecutor.cs`

```csharp
public class AOEProjectileSkillExecutor : ISkillExecutor
{
    public void ExecuteSkill(SkillExecutionContext context)
    {
        // Follow existing pattern from ProjectileSkillExecutor
        var aggregatedEffects = SupportGemProcessor.ProcessSupportGemsZeroGC(
            context.supportGems, context.skillGemData);
        
        // Calculate damage breakdown with support gem effects
        var damageBreakdown = DamageCalculator.Calculate(
            context.skillGemData.baseDamage,
            aggregatedEffects,
            context.playerStats
        );
        
        // Spawn AOE projectile with calculated properties
        SpawnAOEProjectile(context, damageBreakdown, aggregatedEffects);
        
        aggregatedEffects.ReturnToPool();
    }
    
    private void SpawnAOEProjectile(SkillExecutionContext context, 
        DamageBreakdown damage, AggregatedEffects effects)
    {
        var projectile = PoolManager.Instance.SpawnFromPool<AOEProjectile>(
            context.skillGemData.projectilePrefab,
            context.spawnPosition,
            context.aimDirection
        );
        
        // Configure projectile with calculated values
        projectile.InitializeProjectile(
            damage,
            effects,
            context.skillGemData,
            context.supportGems
        );
    }
}
```

### Phase 5: Pool Manager Integration

#### 5.1 Add AOE Prefabs to Pool Configuration
**File**: Update existing PoolManager prefab lists

**Steps**:
1. Create AOEProjectile prefabs in Unity
2. Add to PoolManager's prefab configuration
3. Set initial pool sizes based on skill usage patterns
4. Configure pool growth parameters for dynamic scaling

#### 5.2 Implement ISpawnable Interface
**Implementation**: Ensure proper cleanup in AOEProjectile

```csharp
public override void OnDespawn()
{
    base.OnDespawn(); // Calls parent Projectile cleanup
    
    // AOE-specific cleanup
    if (aoeSystem != null)
    {
        aoeSystem.ResetState();
    }
    
    // Clear pre-allocated arrays
    System.Array.Clear(aoeResults, 0, aoeResults.Length);
    
    // Reset AOE configuration to defaults
    aoeRadius = originalAoeRadius;
    aoeDamageMultiplier = originalAoeDamageMultiplier;
}
```

### Phase 6: Testing and Optimization

#### 6.1 Performance Testing
**Key Metrics**:
- Frame time during multiple AOE explosions
- Garbage collection allocation monitoring
- Memory usage with large AOE radiuses
- Particle system performance impact

**Testing Scenarios**:
- Multiple simultaneous AOE explosions
- Large AOE radius with many enemies
- Rapid-fire AOE projectile skills
- Extended gameplay sessions for memory leaks

#### 6.2 Balance Testing
**Parameters to Validate**:
- AOE damage falloff curves
- Radius scaling with support gems
- Performance impact vs visual quality
- Integration with existing skill progression

## Technical Specifications

### Class Hierarchy
```
Projectile (base class)
├── AOEProjectile (new implementation)
│   ├── Uses AreaDamageSystem for AOE mechanics
│   ├── Integrates with ParticleEffectManager for visuals
│   └── Supports all existing projectile features
```

### Key Performance Targets
- **Zero GC Allocations**: During gameplay execution
- **Maximum Frame Impact**: 2ms additional cost per AOE explosion
- **Memory Footprint**: <1MB additional memory for AOE systems
- **Pool Efficiency**: 95%+ cache hit rate for AOE projectiles

### Integration Points Summary
1. **Projectile System**: Inherit from existing Projectile class
2. **Damage System**: Use DamageBreakdown for proper type handling
3. **Support Gems**: Leverage existing modifier system
4. **Collision**: Use AreaDamageSystem for AOE queries
5. **Visual Effects**: Extend ParticleEffectManager patterns
6. **Object Pooling**: Full integration with PoolManager
7. **Audio**: Ready for future audio manager integration

## Risk Mitigation

### Performance Risks
- **Large AOE Radiuses**: Implement maximum radius limits and LOD for distant explosions
- **Many Simultaneous AOEs**: Stagger processing across frames if needed
- **Particle Overload**: Implement particle budgeting and priority systems

### Integration Risks
- **Support Gem Conflicts**: Test all existing support gem combinations
- **Damage Calculation Edge Cases**: Validate with extreme modifier values
- **Pool Exhaustion**: Implement graceful degradation when pools are full

### Visual Risks
- **Screen Clutter**: Implement visual priority system for overlapping effects
- **Performance Impact**: Use distance-based LOD for particle effects
- **Consistency**: Ensure visual style matches existing projectile effects

## Success Criteria

### Technical Success
- [ ] Zero garbage collection during AOE projectile execution
- [ ] 60 FPS maintained with 10+ simultaneous AOE explosions
- [ ] All existing support gems work correctly with AOE projectiles
- [ ] Memory usage remains stable during extended gameplay

### Functional Success
- [ ] AOE projectiles deal appropriate damage with proper falloff
- [ ] Visual effects provide clear feedback for explosion radius
- [ ] Integration with critical strikes and status effects works correctly
- [ ] Performance remains acceptable on target hardware

### Integration Success
- [ ] Seamless integration with existing skill system
- [ ] Support gem modifications apply correctly to AOE effects
- [ ] No regression in existing projectile functionality
- [ ] Consistent behavior with Path of Exile-style mechanics

## Future Enhancements

### Phase 2 Features (Post-Implementation)
- **Chain AOE**: Explosions that trigger additional explosions
- **Delayed AOE**: Projectiles with timer-based explosions
- **Conditional AOE**: Explosions that trigger based on enemy density
- **Overlapping AOE**: Proper damage stacking for multiple AOE sources

### Advanced Visual Effects
- **Dynamic Particle Scaling**: Particle count based on damage dealt
- **Environmental Integration**: Explosions affect terrain/objects
- **Screen Shake**: Camera effects for impactful explosions
- **Audio Integration**: Positional audio with proper falloff

This implementation plan provides a comprehensive roadmap for adding AOE projectiles while maintaining the project's high performance and architectural standards. The design leverages existing systems maximally while introducing minimal complexity.