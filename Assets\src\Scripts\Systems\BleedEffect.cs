using UnityEngine;

/// <summary>
/// Bleed status effect that applies Physical damage over time.
/// Typically applied by Physical damage type attacks.
/// </summary>
public class BleedEffect : StatusEffect
{
    private float damagePerTick;
    private IDamageable targetHealth;
    
    public BleedEffect(float damagePerTick, float duration = 6f, string sourceId = "") 
        : base(StatusEffectType.Bleed, duration, 1f, sourceId)
    {
        this.damagePerTick = damagePerTick;
    }
    
    protected override void OnApply()
    {
        // Cache the health component - use appropriate method based on object type
        if (target != null)
        {
            // Check if target is player and use PlayerManager
            if (target.CompareTag("Player"))
            {
                var playerHealth = PlayerManager.PlayerHealth;
                
                if (playerHealth != null)
                {
                    targetHealth = playerHealth;
                }
                else
                {
                    targetHealth = null;
                    Debug.LogWarning($"BleedEffect applied to Player but no health component found!");
                }
            }
            else
            {
                // For non-player objects, try PoolManager first, then fallback to GetComponent
                if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
                {
                    targetHealth = combatantHealth;
                }
                else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
                {
                    targetHealth = healthComponent;
                }
                else
                {
                    targetHealth = null;
                    Debug.LogWarning($"BleedEffect applied to {target.name} but no IDamageable component found! Non-pooled objects should use PlayerManager.");
                }
            }
        }
    }
    
    protected override void OnTick()
    {
        if (targetHealth != null && target != null && target.activeInHierarchy)
        {
            // Create damage info for the tick damage with proper breakdown
            DamageInfo tickDamage = DamageInfo.FromSingleType(
                damagePerTick,
                DamageType.Physical,
                false,
                1f,
                $"Bleed_DoT_{SourceId}",
                0f // No ailment chance for DoT ticks
            );
            
            targetHealth.TakeDamage(tickDamage);
        }
    }
    
    protected override void OnRemove()
    {
        // Cleanup if needed
        targetHealth = null;
    }
}
