%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc7afdaae02bac047acf72aabe6a2a9c, type: 3}
  m_Name: IceSpear
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SkillGemData
  icon: {fileID: 2970050062155515212, guid: 5189dbec5e0af694f8baf9e4cdad0fd2, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Ice Spear
  description: Fires a piercing ice projectile that chills enemies
  rarity: 1
  requiresUnlock: 1
  unlockCondition: {fileID: 11400000, guid: ac6a34241f35f3b4bb0321f0f59b9cfd, type: 2}
  skillType: 1
  gemTags: 46
  skillPrefab: {fileID: 2340940985034927489, guid: e25446f6942705a4096dd7ca5694e93f, type: 3}
  baseDamage: 4
  cooldown: 0.75
  manaCost: 4
  projectileSpeed: 15
  duration: 2
  waveAmplitude: 0.08
  waveFrequency: 8
  serpentineDelay: 0.2
  projectileCount: 1
  projectileDelay: 0.1
  orbitRadius: 2
  rotationSpeed: 180
  bladeCount: 3
  targetGroundPosition: 1
  attackSpeedMultiplier: 1
  projectileLayer: 8
  critChance: 15
  critMultiplier: 2
  damageType: 2
  ailmentChance: 20
  intrinsicProjectileCount: 1
  intrinsicSpreadAngle: 20
  intrinsicUseRandomSpread: 0
  intrinsicHasPierce: 0
  intrinsicHasChain: 0
  intrinsicChainCount: 2
  projectileSpeedVariation: 1
  serpentineAmplitudeVariation: 0.2
  serpentineFrequencyVariation: 0.1
  ignitePercent: 0.2
  igniteDuration: 4
  igniteTickInterval: 0.5
  freezeSlowAmount: 0.6
  freezeDuration: 3
  bleedPercent: 0.15
  bleedDuration: 6
  bleedTickInterval: 1
  shockChainDamage: 0.1
  shockChainRange: 3
  shockDuration: 2
