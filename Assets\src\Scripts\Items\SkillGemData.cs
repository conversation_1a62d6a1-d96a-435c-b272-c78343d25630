using UnityEngine;
using Sirenix.OdinInspector;

[CreateAssetMenu(fileName = "New Skill Gem", menuName = "2D Rogue/Items/Skill Gem")]
public class SkillGemData : GemData
{
    [Title("Icon Setup")]
    [InfoBox("Choose between single icon or layered icons (background + foreground). Layered icons provide better visual depth.")]
    [<PERSON><PERSON>("Clear Single Icon")]
    private void ClearSingleIcon() { icon = null; }
    
    [<PERSON><PERSON>("Clear Layered Icons")]  
    private void ClearLayeredIcons() { backgroundIcon = null; foregroundIcon = null; }
    
    [Title("Skill Type")]
    [EnumToggleButtons]
    public SkillType skillType = SkillType.Projectile;
    
    [Title("Gem Tags")]
    [InfoBox("Select which tags this skill gem has. Support gems will only work with matching tags.")]
    [EnumToggleButtons]
    public GemTag gemTags = GemTag.Projectile;
    
    [Title("Skill Properties")]
    [Required("A skill prefab is required for spawning the skill effect")]
    [AssetSelector]
    public GameObject skillPrefab;
    
    public float baseDamage = 10f;
    
    [Range(0.1f, 5f)]
    public float cooldown = 1f;
    
    [Range(0f, 50f)]
    public float manaCost = 10f;
    
    [Title("Projectile Properties")]
    [ShowIf("@skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile")]
    [Range(1f, 50f)]
    public float projectileSpeed = 10f;
    
    [ShowIf("@skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile")]
    [Range(0.1f, 10f)]
    [Tooltip("How long the projectile lives before despawning")]
    public float duration = 2f;
    
    [Title("Serpentine Movement Properties")]
    [ShowIf("skillType", SkillType.SerpentineProjectile)]
    [Range(0.01f, 1f)]
    [Tooltip("Wave amplitude - how wide the serpentine pattern is")]
    public float waveAmplitude = 0.08f;
    
    [ShowIf("skillType", SkillType.SerpentineProjectile)]
    [Range(0f, 30f)]
    [Tooltip("Wave frequency - 0 = straight flight, higher values create tighter curves")]
    public float waveFrequency = 8f;
    
    [ShowIf("skillType", SkillType.SerpentineProjectile)]
    [Range(0f, 2f)]
    [Tooltip("Delay before serpentine movement starts")]
    public float serpentineDelay = 0.2f;
    
    [ShowIf("skillType", SkillType.SerpentineProjectile)]
    [Range(1, 10)]
    [Tooltip("Number of projectiles to fire sequentially")]
    public int projectileCount = 1;
    
    [ShowIf("skillType", SkillType.SerpentineProjectile)]
    [Range(0f, 1f)]
    [Tooltip("Delay between sequential projectiles")]
    public float projectileDelay = 0.1f;
    
    [Title("Orbiting Blade Properties")]
    [ShowIf("skillType", SkillType.OrbitingBlade)]
    [Range(0.5f, 5f)]
    [Tooltip("Distance from player center that blades orbit")]
    public float orbitRadius = 2f;
    
    [ShowIf("skillType", SkillType.OrbitingBlade)]
    [Range(30f, 360f)]
    [Tooltip("Rotation speed in degrees per second")]
    public float rotationSpeed = 180f;
    
    [ShowIf("skillType", SkillType.OrbitingBlade)]
    [Range(1, 8)]
    [Tooltip("Number of blades to spawn around the player")]
    public int bladeCount = 3;
    
    [Title("Instant Skill Properties")]
    [ShowIf("skillType", SkillType.Instant)]
    [InfoBox("If true, skill spawns at target position. If false, spawns at player position.")]
    public bool targetGroundPosition = true;
    
    [Title("Combat Stats")]
    [Range(0.1f, 2f)]
    [Tooltip("Attack speed multiplier for this skill")]
    public float attackSpeedMultiplier = 1f;
    
    [Title("Layer Configuration")]
    [Tooltip("Unity layer for projectiles/spells spawned by this skill. Determines what targets can be damaged.")]
    [InfoBox("Layer 8 = PlayerProjectile, Layer 9 = EnemyProjectile")]
    [Range(0, 31)]
    public int projectileLayer = GameLayers.PlayerProjectile;
    
    /// <summary>
    /// Get the corrected projectile layer (auto-migrates old CollisionLayers values)
    /// </summary>
    public int ProjectileLayer 
    {
        get 
        {
            // Auto-migrate old CollisionLayers enum values
            if (projectileLayer > 31) // Invalid Unity layer, likely old enum value
            {
                int migratedLayer = GameLayers.FromCollisionLayer(projectileLayer);
                Debug.Log($"[SkillGemData] Auto-migrating '{gemName}' projectileLayer from {projectileLayer} to {migratedLayer}");
                return migratedLayer;
            }
            return projectileLayer;
        }
    }
    
    /// <summary>
    /// Get the LayerMask for this projectile layer
    /// </summary>
    public LayerMask ProjectileLayerMask => 1 << ProjectileLayer;
    
    [Range(0f, 100f)]
    [Tooltip("Critical strike chance percentage")]
    public float critChance = 5f;
    
    [Range(1f, 5f)]
    [Tooltip("Critical strike damage multiplier")]
    public float critMultiplier = 2f;
    
    [Title("Damage Type")]
    [EnumToggleButtons]
    public DamageType damageType = DamageType.Physical;
    
    [Title("Ailment Settings")]
    [Range(0f, 100f)]
    [Tooltip("Base chance to inflict ailments (ignite for fire, bleed for physical, etc.)")]
    public float ailmentChance = 10f;

    [Title("Intrinsic Skill Mechanics")]
    [InfoBox("These properties define skill-specific behaviors that work independently of support gems. Use for skills like Spark that naturally fire multiple projectiles.")]
    
    [ShowIf("@skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile")]
    [Range(1, 10)]
    [Tooltip("Number of projectiles this skill fires by default (independent of support gems)")]
    public int intrinsicProjectileCount = 1;
    
    [ShowIf("@(skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile) && intrinsicProjectileCount > 1")]
    [Range(5f, 45f)]
    [Tooltip("Spread angle in degrees for intrinsic multiple projectiles")]
    public float intrinsicSpreadAngle = 20f;
    
    [ShowIf("@(skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile) && intrinsicProjectileCount > 1")]
    [Tooltip("Use random spread instead of even distribution for intrinsic projectiles")]
    public bool intrinsicUseRandomSpread = false;
    
    [ShowIf("@skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile")]
    [Tooltip("Intrinsic piercing - projectiles naturally pierce all enemies")]
    public bool intrinsicHasPierce = false;
    
    [ShowIf("@skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile")]
    [Tooltip("Intrinsic chaining - projectiles naturally chain between enemies")]
    public bool intrinsicHasChain = false;
    
    [ShowIf("@(skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile) && intrinsicHasChain")]
    [Range(1, 5)]
    [Tooltip("Number of times intrinsic projectiles chain")]
    public int intrinsicChainCount = 2;
    
    [Title("Multi-Projectile Variation")]
    [InfoBox("These settings add organic variation to multiple projectiles, making them look less parallel and more natural.")]
    
    [ShowIf("@(skillType == SkillType.Projectile || skillType == SkillType.SerpentineProjectile) && intrinsicProjectileCount > 1")]
    [Range(0f, 5f)]
    [Tooltip("Speed variation range (±). Each projectile gets random speed offset within this range.")]
    public float projectileSpeedVariation = 1f;
    
    [ShowIf("@skillType == SkillType.SerpentineProjectile && intrinsicProjectileCount > 1")]
    [Range(0f, 1f)]
    [Tooltip("Amplitude variation multiplier range (0.8 to 1.2 means ±20% variation)")]
    public float serpentineAmplitudeVariation = 0.2f;
    
    [ShowIf("@skillType == SkillType.SerpentineProjectile && intrinsicProjectileCount > 1")]
    [Range(0f, 0.5f)]
    [Tooltip("Frequency variation multiplier range (0.1 means ±10% variation)")]
    public float serpentineFrequencyVariation = 0.1f;

    [Title("Status Effect Configuration")]
    [InfoBox("Configure status effect parameters for this skill. These values override defaults when this skill applies status effects.")]

    [FoldoutGroup("Ignite (Fire)")]
    [Range(0.05f, 0.5f)]
    [Tooltip("Percentage of damage dealt as ignite DoT (default: 0.2 = 20%)")]
    public float ignitePercent = 0.2f;

    [FoldoutGroup("Ignite (Fire)")]
    [Range(1f, 10f)]
    [Tooltip("Duration of ignite effect in seconds")]
    public float igniteDuration = 4f;

    [FoldoutGroup("Ignite (Fire)")]
    [Range(0.1f, 2f)]
    [Tooltip("Time between ignite damage ticks in seconds")]
    public float igniteTickInterval = 0.5f;

    [FoldoutGroup("Freeze (Ice)")]
    [Range(0.1f, 1f)]
    [Tooltip("Movement speed reduction amount (0.5 = 50% slower)")]
    public float freezeSlowAmount = 0.5f;

    [FoldoutGroup("Freeze (Ice)")]
    [Range(0.5f, 5f)]
    [Tooltip("Duration of freeze effect in seconds")]
    public float freezeDuration = 2f;

    [FoldoutGroup("Bleed (Physical)")]
    [Range(0.05f, 0.3f)]
    [Tooltip("Percentage of damage dealt as bleed DoT (default: 0.15 = 15%)")]
    public float bleedPercent = 0.15f;

    [FoldoutGroup("Bleed (Physical)")]
    [Range(2f, 10f)]
    [Tooltip("Duration of bleed effect in seconds")]
    public float bleedDuration = 6f;

    [FoldoutGroup("Bleed (Physical)")]
    [Range(0.1f, 2f)]
    [Tooltip("Time between bleed damage ticks in seconds")]
    public float bleedTickInterval = 1f;

    [FoldoutGroup("Shock (Lightning)")]
    [Range(0.05f, 0.25f)]
    [Tooltip("Percentage of damage for chain lightning (default: 0.1 = 10%)")]
    public float shockChainDamage = 0.1f;

    [FoldoutGroup("Shock (Lightning)")]
    [Range(1f, 8f)]
    [Tooltip("Range for chain lightning in units")]
    public float shockChainRange = 3f;

    [FoldoutGroup("Shock (Lightning)")]
    [Range(0.5f, 5f)]
    [Tooltip("Duration of shock effect in seconds")]
    public float shockDuration = 2f;
    
    [Title("Support Gem Slots")]
    [ReadOnly]
    [ShowInInspector]
    [PropertySpace]
    [InfoBox("Support slots are determined by instance rarity (not template rarity).\nSlot counts are configured in SupportSlotConfiguration asset.\nThis property is obsolete - use GemInstance.GetSupportSlotCount() instead.")]
    [System.Obsolete("Use GemInstance.GetSupportSlotCount() instead. This property uses obsolete template rarity.")]
    public int SupportSlotCount => (int)rarity;

    /// <summary>
    /// Get the status effect configuration for this skill gem.
    /// Uses default values for backward compatibility with existing assets.
    /// </summary>
    public StatusEffectHelper.StatusEffectConfig GetStatusEffectConfig()
    {
        return new StatusEffectHelper.StatusEffectConfig
        {
            // Use configured values or defaults for backward compatibility
            ignitePercent = this.ignitePercent > 0 ? this.ignitePercent : 0.2f,
            igniteDuration = this.igniteDuration > 0 ? this.igniteDuration : 4f,
            igniteTickInterval = this.igniteTickInterval > 0 ? this.igniteTickInterval : 0.5f,

            freezeSlowAmount = this.freezeSlowAmount > 0 ? this.freezeSlowAmount : 0.5f,
            freezeDuration = this.freezeDuration > 0 ? this.freezeDuration : 2f,

            bleedPercent = this.bleedPercent > 0 ? this.bleedPercent : 0.15f,
            bleedDuration = this.bleedDuration > 0 ? this.bleedDuration : 6f,
            bleedTickInterval = this.bleedTickInterval > 0 ? this.bleedTickInterval : 1f,

            shockChainDamage = this.shockChainDamage > 0 ? this.shockChainDamage : 0.1f,
            shockChainRange = this.shockChainRange > 0 ? this.shockChainRange : 3f,
            shockDuration = this.shockDuration > 0 ? this.shockDuration : 2f
        };
    }
    
    public override string GetTooltipText()
    {
        string tooltip = $"<color=#{ColorUtility.ToHtmlStringRGB(GetRarityColor())}>{gemName}</color>\n" +
           $"Type: {skillType}";
           
        // Add gem tags display
        if (gemTags != GemTag.None)
        {
            var tagNames = new System.Collections.Generic.List<string>();
            if ((gemTags & GemTag.Melee) != 0) tagNames.Add(GemTag.Melee.GetColoredDisplayName());
            if ((gemTags & GemTag.Projectile) != 0) tagNames.Add(GemTag.Projectile.GetColoredDisplayName());
            if ((gemTags & GemTag.Spell) != 0) tagNames.Add(GemTag.Spell.GetColoredDisplayName());
            if ((gemTags & GemTag.Duration) != 0) tagNames.Add(GemTag.Duration.GetColoredDisplayName());
            if ((gemTags & GemTag.AreaOfEffect) != 0) tagNames.Add(GemTag.AreaOfEffect.GetColoredDisplayName());
            if ((gemTags & GemTag.Ailment) != 0) tagNames.Add(GemTag.Ailment.GetColoredDisplayName());
            if ((gemTags & GemTag.Physical) != 0) tagNames.Add(GemTag.Physical.GetColoredDisplayName());
            if ((gemTags & GemTag.Fire) != 0) tagNames.Add(GemTag.Fire.GetColoredDisplayName());
            if ((gemTags & GemTag.Cold) != 0) tagNames.Add(GemTag.Cold.GetColoredDisplayName());
            if ((gemTags & GemTag.Lightning) != 0) tagNames.Add(GemTag.Lightning.GetColoredDisplayName());
            tooltip += $"\nTags: {string.Join(", ", tagNames)}";
        }
        
        tooltip += $"\n\n{description}\n\n" +
           TooltipFormatter.FormatStat("Damage", $"{baseDamage} ({damageType})") + "\n" +
           TooltipFormatter.FormatStat("Cooldown", cooldown, "F0", "s") + "\n" +
           TooltipFormatter.FormatStat("Mana Cost", manaCost, "F0") + "\n" +
           TooltipFormatter.FormatStat("Attack Speed", attackSpeedMultiplier, "F1", "x") + "\n" +
           TooltipFormatter.FormatStat("Critical Chance", critChance, "F1", "%") + "\n" +
           TooltipFormatter.FormatStat("Critical Multiplier", critMultiplier, "F1", "x");
           
        // Add duration for skills that have it AND the Duration tag
        if (duration > 0f && (gemTags & GemTag.Duration) != 0)
        {
            tooltip += "\n" + TooltipFormatter.FormatStat("Duration", duration, "F1", "s");
        }

        // Add status effect information using new breakdown-aware method
        if (ailmentChance > 0)
        {
            // Create simple breakdown for single damage type
            var simpleBreakdown = new DamageBreakdown(baseDamage, damageType);

            // Create status effect config from skill gem data
            var statusConfig = new StatusEffectHelper.StatusEffectConfig
            {
                ignitePercent = ignitePercent,
                igniteDuration = igniteDuration,
                igniteTickInterval = igniteTickInterval,
                freezeSlowAmount = freezeSlowAmount,
                freezeDuration = freezeDuration,
                bleedPercent = bleedPercent,
                bleedDuration = bleedDuration,
                bleedTickInterval = bleedTickInterval,
                shockChainDamage = shockChainDamage,
                shockChainRange = shockChainRange,
                shockDuration = shockDuration
            };

            string ailmentInfo = TooltipFormatter.FormatAilmentChances(simpleBreakdown, ailmentChance, statusConfig);
            if (!string.IsNullOrEmpty(ailmentInfo))
            {
                tooltip += "\n\n" + ailmentInfo;
            }
        }
        
        if (skillType == SkillType.Projectile)
        {
        tooltip += "\n\n" + TooltipFormatter.FormatStat("Projectile Speed", projectileSpeed);
        }
        else if (skillType == SkillType.SerpentineProjectile)
        {
        tooltip += "\n\n" + TooltipFormatter.FormatStat("Projectile Speed", projectileSpeed) + "\n" +
              TooltipFormatter.FormatStat("Wave Amplitude", waveAmplitude, "F3") + "\n" +
              TooltipFormatter.FormatStat("Wave Frequency", waveFrequency, "F1") + "\n" +
              TooltipFormatter.FormatStat("Serpentine Delay", serpentineDelay, "F1", "s");
        
        if (projectileCount > 1)
        {
            tooltip += "\n" + TooltipFormatter.FormatStat("Sequential Projectiles", projectileCount) + "\n" +
                      TooltipFormatter.FormatStat("Projectile Delay", projectileDelay, "F2", "s");
        }
        }
        else if (skillType == SkillType.OrbitingBlade)
        {
        tooltip += "\n\n" + TooltipFormatter.FormatStat("Orbit Radius", orbitRadius, "F1") + "\n" +
              TooltipFormatter.FormatStat("Rotation Speed", rotationSpeed, "F0", "°/s") + "\n" +
              TooltipFormatter.FormatStat("Blade Count", bladeCount);
        }
        else if (skillType == SkillType.Instant && targetGroundPosition)
        {
        tooltip += "\n\nTargets ground position";
        }
        
        return tooltip;
    }
}