/// <summary>
/// Defines how projectiles process collision hits
/// </summary>
public enum HitProcessingMode
{
    /// <summary>
    /// Process only the first collision hit (standard projectiles)
    /// </summary>
    FirstHitOnly,
    
    /// <summary>
    /// Process all collision hits (rare, expensive)
    /// </summary>
    AllHits,
    
    /// <summary>
    /// Process up to maximum hit limit (piercing projectiles)  
    /// </summary>
    MaxHits
}