fileFormatVersion: 2
guid: ea894b6ff3db2f642b6f18e25de85b0a
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 8020143640844766479
    second: spritesheet_0
  - first:
      213: -6251682230944352461
    second: spritesheet_1
  - first:
      213: -1259408508766424310
    second: spritesheet_2
  - first:
      213: -5962389079421231736
    second: spritesheet_3
  - first:
      213: 2788568348960109317
    second: spritesheet_4
  - first:
      213: 226702753830753226
    second: spritesheet_5
  - first:
      213: -7921052967969465774
    second: spritesheet_6
  - first:
      213: -1370661682523340017
    second: spritesheet_7
  - first:
      213: -8276262488406422588
    second: spritesheet_8
  - first:
      213: -5906973516983679737
    second: spritesheet_9
  - first:
      213: 5777552420452043588
    second: spritesheet_10
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: YellowArrow_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eb7ff5ec7fae657498cb7c93862529a8
      internalID: -2018480788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_1
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 87c0954f43814034ba65845723e9dc5e
      internalID: 2084370457
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_2
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fb09e7d2134bb3647a94e4100b801ea5
      internalID: -559723607
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_3
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5de51444f510f1143857004f376888c4
      internalID: -977491134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_4
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eb8d6a28b9c4e494aacb0b74a0818483
      internalID: -232002787
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_5
      rect:
        serializedVersion: 2
        x: 160
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ea71d66a8c7396248ad23980cdcf32bc
      internalID: -1911779446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_6
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 58d828cce37a2c04f894ba30dc5d6a9d
      internalID: 1906863233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_7
      rect:
        serializedVersion: 2
        x: 224
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8db2cc57193355a46b23f959591f49d9
      internalID: 279857682
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_8
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4296997de71b96b45a4313664bc6eae5
      internalID: -1349033361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_9
      rect:
        serializedVersion: 2
        x: 288
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4f3cda009c48c8f49a07a54ec7df4be6
      internalID: -440145636
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_10
      rect:
        serializedVersion: 2
        x: 320
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ad4ef2018cb470a4baf36a5662ccfa4e
      internalID: -219955517
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: YellowArrow_11
      rect:
        serializedVersion: 2
        x: 352
        y: 0
        width: 32
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9e80bdc3ef9c94644b6c4e14c6000d79
      internalID: 1162431283
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: f596f09e91f0be44eae59ede00f5b539
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":32.0,"y":12.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      YellowArrow_0: -2018480788
      YellowArrow_1: 2084370457
      YellowArrow_10: -219955517
      YellowArrow_11: 1162431283
      YellowArrow_2: -559723607
      YellowArrow_3: -977491134
      YellowArrow_4: -232002787
      YellowArrow_5: -1911779446
      YellowArrow_6: 1906863233
      YellowArrow_7: 279857682
      YellowArrow_8: -1349033361
      YellowArrow_9: -440145636
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
