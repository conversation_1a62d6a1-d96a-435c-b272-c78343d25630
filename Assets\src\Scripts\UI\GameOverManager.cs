using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using Sirenix.OdinInspector;
using System.Collections.Generic;

/// <summary>
/// Manages the Game Over UI screen with restart and menu options.
/// Displays when player dies and pauses the game.
/// </summary>
public class GameOverManager : MonoBehaviour
{
    [Title("UI References")]
    [SerializeField, Required] private GameObject gameOverPanel;
    [SerializeField, Required] private Button restartButton;
    [SerializeField, Required] private Button menuButton;
    
    [Title("Optional UI Elements")]
    [SerializeField] private TMP_Text gameOverTitle;
    [SerializeField] private TMP_Text gameOverMessage;
    [SerializeField] private TMP_Text levelValueText;
    [SerializeField] private TMP_Text unlockedCountText;
    
    [Title("Scene Management")]
    [SerializeField] private string currentSceneName = "";
    [SerializeField] private string menuSceneName = "MainMenu";
    
    [Title("Player Stats")]
    [SerializeField] private PlayerStats playerStats;
    
    
    [Title("Audio (Optional)")]
    [SerializeField] private AudioClip gameOverSound;
    [SerializeField] private AudioSource audioSource;
    
    // Singleton pattern for easy access
    public static GameOverManager Instance { get; private set; }
    
    private bool isGameOver = false;
    
    // Unlock counter
    private int sessionUnlockedCount = 0;
    
    private void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Debug.LogWarning("Multiple GameOverManager instances found! Destroying duplicate.");
            Destroy(gameObject);
            return;
        }
        
        // Get current scene name if not set
        if (string.IsNullOrEmpty(currentSceneName))
        {
            currentSceneName = SceneManager.GetActiveScene().name;
        }
        
        // Auto-find audio source if not assigned
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
        }
        
        // Auto-find PlayerStats if not assigned
        if (playerStats == null)
        {
            playerStats = FindObjectOfType<PlayerStats>();
            if (playerStats == null)
            {
                Debug.LogWarning("GameOverManager: PlayerStats not found! Level will not be displayed in Game Over screen.");
            }
        }
    }
    
    private void Start()
    {
        // Ensure game over panel is hidden at startup
        if (gameOverPanel != null)
        {
            gameOverPanel.SetActive(false);
        }
        
        // Setup button listeners
        if (restartButton != null)
        {
            restartButton.onClick.AddListener(RestartGame);
        }
        
        if (menuButton != null)
        {
            menuButton.onClick.AddListener(ReturnToMenu);
        }
        
        // Set default text if components exist but no text assigned
        if (gameOverTitle != null && string.IsNullOrEmpty(gameOverTitle.text))
        {
            gameOverTitle.text = "GAME OVER";
        }
        
        if (gameOverMessage != null && string.IsNullOrEmpty(gameOverMessage.text))
        {
            gameOverMessage.text = "You have died. Choose your next action.";
        }
        
        // Subscribe to unlock events
        SubscribeToUnlockEvents();
    }
    
    /// <summary>
    /// Triggers the game over state - called from PlayerHealth
    /// </summary>
    public void TriggerGameOver()
    {
        if (isGameOver) return; // Prevent multiple triggers
        
        isGameOver = true;
        
        // Pause the game
        Time.timeScale = 0f;
        
        // Show game over panel
        if (gameOverPanel != null)
        {
            gameOverPanel.SetActive(true);
        }
        
        // Update level display
        UpdateLevelDisplay();
        
        // Update unlocked count display
        UpdateUnlockedCountDisplay();
        
        // Play game over sound
        if (gameOverSound != null && audioSource != null)
        {
            // Use unscaled time for audio during pause
            audioSource.PlayOneShot(gameOverSound);
        }
        
        // Focus on restart button for keyboard navigation
        if (restartButton != null)
        {
            restartButton.Select();
        }
        
        Debug.Log("Game Over triggered - game paused");
    }
    
    /// <summary>
    /// Restart the current scene
    /// </summary>
    public void RestartGame()
    {
        Debug.Log("Restarting game...");
        
        // Resume time before loading scene
        Time.timeScale = 1f;
        isGameOver = false;
        
        // Reload current scene
        SceneManager.LoadScene(currentSceneName);
    }
    
    /// <summary>
    /// Return to main menu
    /// </summary>
    public void ReturnToMenu()
    {
        Debug.Log("Returning to menu...");
        
        // Resume time before loading scene
        Time.timeScale = 1f;
        isGameOver = false;
        
        // Load menu scene
        SceneManager.LoadScene(menuSceneName);
    }
    
    /// <summary>
    /// Check if game is currently in game over state
    /// </summary>
    public bool IsGameOver => isGameOver;
    
    /// <summary>
    /// Updates the level display in the game over screen
    /// </summary>
    private void UpdateLevelDisplay()
    {
        if (levelValueText != null && playerStats != null)
        {
            // Use garbage-free method if available, otherwise use standard text setting
            levelValueText.SetTextNoAlc(playerStats.Level);
        }
        else if (levelValueText != null)
        {
            // Fallback if PlayerStats is not available
            levelValueText.text = "?";
        }
    }
    
    /// <summary>
    /// Subscribe to unlock events from ProgressTracker
    /// </summary>
    private void SubscribeToUnlockEvents()
    {
        ProgressTracker.OnGemUnlocked += OnGemUnlocked;
    }
    
    /// <summary>
    /// Unsubscribe from unlock events
    /// </summary>
    private void UnsubscribeFromUnlockEvents()
    {
        ProgressTracker.OnGemUnlocked -= OnGemUnlocked;
    }
    
    /// <summary>
    /// Called when a gem is unlocked during gameplay
    /// </summary>
    private void OnGemUnlocked(object sender, UnlockEventArgs e)
    {
        if (e.UnlockedGem != null)
        {
            sessionUnlockedCount++;
            Debug.Log($"GameOverManager: Tracked new unlock - {e.UnlockedGem.gemName} (Total: {sessionUnlockedCount})");
        }
    }
    
    /// <summary>
    /// Updates the unlocked gems counter display
    /// </summary>
    private void UpdateUnlockedCountDisplay()
    {
        if (unlockedCountText != null)
        {
            // Always show unlock count (including 0)
            unlockedCountText.text = $"Unlocked: {sessionUnlockedCount}";
        }
    }
    
    private void OnDestroy()
    {
        // Unsubscribe from unlock events
        UnsubscribeFromUnlockEvents();
        
        // Cleanup button listeners
        if (restartButton != null)
        {
            restartButton.onClick.RemoveListener(RestartGame);
        }
        
        if (menuButton != null)
        {
            menuButton.onClick.RemoveListener(ReturnToMenu);
        }
        
        // Clear singleton reference
        if (Instance == this)
        {
            Instance = null;
        }
        
        // Ensure time scale is restored if object is destroyed
        if (isGameOver)
        {
            Time.timeScale = 1f;
        }
    }
    
    #if UNITY_EDITOR
    [Title("Debug Tools")]
    [FoldoutGroup("Debug")]
    [Button("Test Game Over", ButtonSizes.Medium)]
    private void TestGameOver()
    {
        if (Application.isPlaying)
        {
            TriggerGameOver();
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Restart", ButtonSizes.Small)]
    private void TestRestart()
    {
        if (Application.isPlaying)
        {
            RestartGame();
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Menu", ButtonSizes.Small)]
    private void TestMenu()
    {
        if (Application.isPlaying)
        {
            ReturnToMenu();
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Level Display", ButtonSizes.Small)]
    private void TestLevelDisplay()
    {
        if (Application.isPlaying)
        {
            UpdateLevelDisplay();
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Unlock Count", ButtonSizes.Small)]
    private void TestUnlockCount()
    {
        if (Application.isPlaying)
        {
            UpdateUnlockedCountDisplay();
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Simulate Gem Unlock", ButtonSizes.Small)]
    private void SimulateGemUnlock()
    {
        if (Application.isPlaying)
        {
            sessionUnlockedCount++;
            UpdateUnlockedCountDisplay();
            Debug.Log($"Simulated unlock - Total count: {sessionUnlockedCount}");
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Reset Unlock Count", ButtonSizes.Small)]
    private void ResetUnlockCount()
    {
        if (Application.isPlaying)
        {
            sessionUnlockedCount = 0;
            UpdateUnlockedCountDisplay();
            Debug.Log("Reset unlock count to 0");
        }
    }
    
    [FoldoutGroup("Debug")]
    [ShowInInspector]
    [PropertySpace]
    private string GameOverStatus => 
        $"Game Over State: {(isGameOver ? "Active" : "Inactive")}\n" +
        $"Time Scale: {Time.timeScale:F1}\n" +
        $"Current Scene: {currentSceneName}\n" +
        $"Menu Scene: {menuSceneName}\n" +
        $"Panel Active: {(gameOverPanel != null ? gameOverPanel.activeSelf.ToString() : "No Panel")}\n" +
        $"PlayerStats: {(playerStats != null ? "✓" : "✗")}\n" +
        $"Current Level: {(playerStats != null ? playerStats.Level.ToString() : "N/A")}\n" +
        $"Level Text: {(levelValueText != null ? "✓" : "✗")}\n" +
        $"---Unlock Counter---\n" +
        $"Session Unlocks: {sessionUnlockedCount}\n" +
        $"Counter Text: {(unlockedCountText != null ? "✓" : "✗")}";
    #endif
}