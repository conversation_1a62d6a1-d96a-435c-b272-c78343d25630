# Post-Deletion Collision Implementation Task List

## 🚨 CRITICAL FINDINGS 

**Sub-Agent <PERSON><PERSON><PERSON> haben ergeben:**
- **15+ zusätzliche übersehene Dateien** im ursprünglichen Plan
- **220 direkte Referenzen** in **36 Dateien** 
- **ProjectileMigrated.cs** bereits auf Unity Physics2D migriert (GELÖSCHT aber wichtig!)
- **Migration ist deutlich komplexer** als ursprünglich geplant

---

## 📋 TASK LIST: Post-Deletion Collision Implementation

### PHASE 0: KRITISCHE KLÄRUNG (SOFORT ERFORDERLICH)

| Task ID | Priority | Script | Beschreibung |
|---------|-----------|---------|---------------|
| **P0-01** | KRITISCH | `ProjectileMigrated.cs` | ✅ **BEREITS GELÖSCHT** - Prüfe ob das der neue Standard war |
| **P0-02** | KRITISCH | `MeleeAttackStrategyMigrated.cs` | ✅ **BEREITS GELÖSCHT** - Migration erfolgt |
| **P0-03** | KRITISCH | `HitProcessingMode.cs` | Prüfe ob schon implementiert - wird von Migrated Files referenziert |

---

### PHASE 1: GAMEPLAY-CRITICAL SYSTEMS (Spiel ist unspielbar ohne diese)

#### 🎯 Area Damage System - HÖCHSTE PRIORITÄT
| Task ID | Priority | Script | Implementierung | Zeitaufwand |
|---------|-----------|---------|-----------------|-------------|
| **P1-01** | KRITISCH | `Projectile.cs` | `CollisionManager.GetCollidersInRadiusNonAlloc()` → `Physics2D.OverlapCircleNonAlloc()` | 4-6h |
| **P1-02** | KRITISCH | `InstantSpell.cs` | `SpatialCollider` trigger → `Collider2D` mit `OnTriggerEnter2D` | 3-4h |
| **P1-03** | KRITISCH | `MagmaBall.cs` | Target search → `Physics2D.OverlapCircleNonAlloc()` | 2-3h |
| **P1-04** | KRITISCH | `OrbitingBlade.cs` | ICollidable cache → Unity Physics2D queries | 2-3h |
| **P1-05** | KRITISCH | `SerpentineProjectile.cs` | `CollisionManager` → Unity Physics2D | 2-3h |
| **P1-06** | KRITISCH | `StationaryTurret.cs` | ICollidable Liste → Physics2D target detection | 2-3h |

**Migration Pattern für alle Projectiles:**
```csharp
// ALT: CollisionManager.Instance.GetCollidersInRadiusNonAlloc(position, radius, _nearbyEnemiesCache)
// NEU: Physics2D.OverlapCircleNonAlloc(position, radius, results, enemyLayerMask)

// ALT: ISpatialCollisionHandler.HandleCollisionEnter(CollisionInfo info)
// NEU: OnTriggerEnter2D(Collider2D other)

// ALT: [RequireComponent(typeof(SpatialCollider))]
// NEU: [RequireComponent(typeof(Collider2D))]
```

#### ⚔️ Enemy Combat System - HÖCHSTE PRIORITÄT
| Task ID | Priority | Script | Implementierung | Zeitaufwand |
|---------|-----------|---------|-----------------|-------------|
| **P1-07** | KRITISCH | `BaseEnemy.cs` | `[RequireComponent(typeof(SpatialCollider))]` → `Collider2D` | 1h |
| **P1-08** | KRITISCH | `MeleeAttackStrategy.cs` | PlayerManager SpatialCollider → Physics2D player detection | 3-4h |
| **P1-09** | KRITISCH | `RangedAttackStrategy.cs` | PlayerManager SpatialCollider → Layer-based targeting | 2-3h |

**Enemy AI Pattern:**
```csharp
// ALT: PlayerManager.Instance.PlayerSpatialCollider position/radius checks
// NEU: Physics2D.OverlapCircle(transform.position, attackRange, playerLayerMask)
```

---

### PHASE 2: ECONOMY & LOOT SYSTEMS (Spieler kann nichts aufsammeln)

#### 💰 Pickup System - HOHE PRIORITÄT
| Task ID | Priority | Script | Implementierung | Zeitaufwand |
|---------|-----------|---------|-----------------|-------------|
| **P2-01** | HOCH | `CurrencyPickup.cs` | `ISpatialCollisionHandler` → `OnTriggerEnter2D` | 2h |
| **P2-02** | HOCH | `SplinterPickup.cs` | Trigger detection → Unity trigger system | 2h |
| **P2-03** | MITTEL | `ChestComponent.cs` | Player interaction → `OnTriggerEnter2D/Exit2D` | 2h |
| **P2-04** | MITTEL | `ShopTrigger.cs` | Shop detection → Unity trigger range | 1h |

**Pickup Pattern:**
```csharp
// ALT: ISpatialCollisionHandler.HandleTriggerEnter(CollisionInfo collision)
// NEU: OnTriggerEnter2D(Collider2D other) { if (other.CompareTag("Player")) ... }
```

---

### PHASE 3: EFFECTS & UTILITY SYSTEMS (Visuelle & Support-Features)

#### ✨ Effect System - MITTLERE PRIORITÄT  
| Task ID | Priority | Script | Implementierung | Zeitaufwand |
|---------|-----------|---------|-----------------|-------------|
| **P3-01** | MITTEL | `BreachEffectController.cs` | `SpatialCollider` → `Collider2D` | 1-2h |
| **P3-02** | MITTEL | `BreachEnemySpawner.cs` | ICollidable cache → Physics2D queries | 2-3h |
| **P3-03** | MITTEL | `BreachRadiusAnimator.cs` | Radius animation → Unity trigger | 1-2h |
| **P3-04** | NIEDRIG | `TriggerScaleAnimation.cs` | SpatialCollider referenz → Collider2D | 1h |
| **P3-05** | NIEDRIG | `PrimeTweenEffect.cs` | `ISpatialCollisionHandler` → Unity callbacks | 1-2h |

---

### PHASE 4: CORE SYSTEMS UPDATE (Manager & Player Integration)

#### 🎮 Player & Manager Systems - MITTLERE PRIORITÄT
| Task ID | Priority | Script | Implementierung | Zeitaufwand |
|---------|-----------|---------|-----------------|-------------|
| **P4-01** | HOCH | `PlayerController.cs` | `[RequireComponent(typeof(SpatialCollider))]` → `Collider2D` | 1h |
| **P4-02** | HOCH | `PlayerManager.cs` | `SpatialCollider playerSpatialCollider` cache → `Collider2D` | 2-3h |
| **P4-03** | MITTEL | `ChunkContentSpawner.cs` | CollisionManager cache → Direct Physics2D | 1-2h |
| **P4-04** | MITTEL | `PlayerBuffSystem.cs` | ICollidable cache → Physics2D | 1h |
| **P4-05** | MITTEL | `SkillExecutor.cs` | CollisionManager integration → Physics2D | 3-4h |

---

### PHASE 5: SUPPORT SYSTEMS (Tools, Tests, Documentation)

#### 🔧 Editor Tools & Testing - NIEDRIGE PRIORITÄT
| Task ID | Priority | Script | Implementierung | Zeitaufwand |
|---------|-----------|---------|-----------------|-------------|
| **P5-01** | NIEDRIG | `ChestCreationTool.cs` | SpatialCollider configuration → Collider2D | 1h |
| **P5-02** | NIEDRIG | `QuickCreateChests.cs` | SpatialCollider setup → Unity components | 1h |
| **P5-03** | NIEDRIG | `AutonomousSupportGemTest.cs` | CollisionManager tests → Physics2D tests | 2h |

---

## 🆘 ZUSÄTZLICHE ÜBERSEHENE DATEIEN (Sub-Agent Findings)

### Attack Strategies (ÜBERSEHEN im ursprünglichen Plan!)
| Task ID | Priority | Script | Problem | Lösung |
|---------|-----------|---------|---------|---------|
| **PA-01** | HOCH | `AgentSpawnAttackStrategy.cs` | `CollisionLayers.EnemyProjectile` | Layer Mask System |
| **PA-02** | HOCH | `FireRainStrategy.cs` | `CollisionLayers.EnemyProjectile` | Layer Mask System |
| **PA-03** | MITTEL | `IAttackStrategy.cs` | Interface mit CollisionLayers | Interface aktualisieren |

### Skill Executors (ÜBERSEHEN!)  
| Task ID | Priority | Script | Problem | Lösung |
|---------|-----------|---------|---------|---------|
| **PS-01** | MITTEL | `InstantSkillExecutor.cs` | Collision dependencies | Unity Physics2D |
| **PS-02** | MITTEL | `ProjectileSkillExecutor.cs` | Collision integration | Physics2D integration |
| **PS-03** | MITTEL | `OrbitingBladeSkillExecutor.cs` | Spatial system usage | Direct Physics2D |

### Testing & Validation (ÜBERSEHEN!)
| Task ID | Priority | Script | Problem | Lösung |
|---------|-----------|---------|---------|---------|
| **PT-01** | NIEDRIG | `CurrencyPickupSystemTest.cs` | Collision integration tests | Physics2D test updates |
| **PT-02** | NIEDRIG | `Phase2SetupGuide.cs` | SpatialCollider setup guide | Aktualisieren auf Collider2D |

---

## ⏱️ ZEITSCHÄTZUNG KOMPLETT

| Phase | Arbeitsstunden | Tage (8h/Tag) | Kritikalität |
|--------|----------------|---------------|--------------|
| **Phase 0** | 2-4h | 0.5 Tage | SOFORT |
| **Phase 1** | 20-28h | 3-4 Tage | KRITISCH |
| **Phase 2** | 7-9h | 1 Tag | HOCH |
| **Phase 3** | 6-10h | 1-1.5 Tage | MITTEL |
| **Phase 4** | 8-12h | 1-1.5 Tage | MITTEL |
| **Phase 5** | 4-6h | 0.5-1 Tag | NIEDRIG |
| **Zusätzliche** | 6-10h | 1 Tag | VARIABEL |
| **TOTAL** | **53-79h** | **8-11 Tage** | - |

---

## 🔄 IMPLEMENTATION STRATEGY

### Sequential Implementation (Empfohlen)
1. **Phase 0 SOFORT** - Klärung der Migration-Situation
2. **Phase 1 KRITISCH** - Area Damage & Enemy Combat (Spiel funktioniert wieder)  
3. **Phase 2 HOCH** - Pickup System (Vollständiges Gameplay)
4. **Phase 3-5 PARALLEL** - Support Systems (Polish & Tools)

### Unity Physics2D Replacement Pattern
```csharp
// GLOBAL REPLACEMENT PATTERN:

// 1. Component Requirement
[RequireComponent(typeof(SpatialCollider))] → [RequireComponent(typeof(Collider2D))]

// 2. Interface Implementation  
: ISpatialCollisionHandler → MonoBehaviour mit Unity trigger events

// 3. Collision Detection
CollisionManager.Instance.GetCollidersInRadiusNonAlloc() → Physics2D.OverlapCircleNonAlloc()

// 4. Layer System
CollisionLayers enum → Unity LayerMask mit Physics2D.OverlapCircle(pos, radius, layerMask)

// 5. Event Handling
HandleCollisionEnter(CollisionInfo info) → OnTriggerEnter2D(Collider2D other)
```

---

## ⚠️ MIGRATION RISKS & MITIGATION

### Performance Risks
- **Zero-GC Verlust**: Unity Physics2D alloziert GC - Use `NonAlloc` variants
- **Spatial Partitioning Verlust**: Verwende Unity's LayerMask für Optimierung
- **60 FPS Requirement**: Frequent testing während Migration

### Functionality Risks
- **Area Damage Broken**: Höchste Priorität - Spiel unspielbar ohne
- **Enemy AI Broken**: Enemies können nicht angreifen
- **Loot System Broken**: Spieler kann keine Items sammeln

### Integration Risks  
- **Animation Events**: Frame-perfect timing muss beibehalten werden
- **Manager Dependencies**: PlayerManager cache muss ersetzt werden
- **Prefab Dependencies**: Alle Prefabs mit RequireComponent müssen aktualisiert werden

---

## ✅ SUCCESS CRITERIA

**Migration erfolgreich wenn:**
- [ ] Alle 36+ Dateien kompilieren ohne Errors
- [ ] Projectile Area Damage funktioniert
- [ ] Enemy AI greift Spieler an
- [ ] Pickup System sammelt Items
- [ ] Performance bleibt bei 60 FPS  
- [ ] Alle Unit Tests bestehen
- [ ] Zero-GC Requirements erfüllt (mit NonAlloc variants)

**Estimated Total Effort: 8-11 Arbeitstage** für vollständige Migration des Collision Systems auf Unity Physics2D.