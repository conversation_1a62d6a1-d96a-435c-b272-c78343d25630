# Unity Layer Setup Instructions

## Current Issue
The GameLayers constants are trying to use layers 3-14, but Unity's TagManager only has layers 0-6 configured.

## Required Unity TagManager Configuration

You need to configure the following layers in **Edit → Project Settings → Tags and Layers**:

### **Layer Configuration:**
```
Layer 0:  Default
Layer 1:  TransparentFX  
Layer 2:  Ignore Raycast
Layer 3:  [Empty] → Set to "Player"
Layer 4:  Water → Change to "Enemy"
Layer 5:  UI → Change to "PlayerProjectile" 
Layer 6:  Players → Change to "EnemyProjectile"
Layer 7:  [Empty] → Set to "Wall"
Layer 8:  [Empty] → Set to "Trigger"
Layer 9:  [Empty] → Set to "Pickup"
Layer 10: [Empty] → Set to "Environment"
Layer 11: [Empty] → Set to "Interactable"
Layer 12: [Empty] → Set to "Vegetation"
Layer 13: [Empty] → Set to "Breach"
Layer 14: [Empty] → Set to "Shop"
```

### **Steps to Configure:**
1. Open **Edit → Project Settings → Tags and Layers**
2. Update the layer names as shown above
3. Click "Apply" to save changes

### **Physics2D Collision Matrix Setup:**
After configuring layers, set up collision rules in **Edit → Project Settings → Physics2D → Layer Collision Matrix**:

- ✅ Player ↔ Enemy (Player can be hit by enemies)
- ✅ Player ↔ EnemyProjectile (Player can be hit by enemy projectiles)  
- ✅ Player ↔ Wall (Player collides with walls)
- ✅ Player ↔ Environment (Player collides with environment)
- ✅ Player ↔ Pickup (Player can pickup items)
- ✅ Player ↔ Interactable (Player can interact with objects)
- ✅ Player ↔ Shop (Player can enter shops)

- ✅ Enemy ↔ PlayerProjectile (Enemies can be hit by player projectiles)
- ✅ Enemy ↔ Wall (Enemies collide with walls)
- ✅ Enemy ↔ Environment (Enemies collide with environment)

- ✅ PlayerProjectile ↔ Wall (Player projectiles hit walls)
- ✅ PlayerProjectile ↔ Environment (Player projectiles hit environment)

- ✅ EnemyProjectile ↔ Wall (Enemy projectiles hit walls)
- ✅ EnemyProjectile ↔ Environment (Enemy projectiles hit environment)

## Alternative: Use Available Layers

If you prefer not to reconfigure TagManager, I can update GameLayers to use the currently available layers:

```csharp
// Use existing Unity layers
public const int Player = 6;        // Currently "Players"
public const int Enemy = 4;         // Currently "Water" 
public const int PlayerProjectile = 5;  // Currently "UI"
public const int EnemyProjectile = 3;   // Currently empty
// etc.
```

**Recommendation:** Configure the TagManager properly for cleaner layer names and better organization.