using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Order-independent support gem effect processor.
/// Eliminates socket order dependencies by collecting and applying effects in standardized priority order.
/// Includes object pooling for zero-GC operation.
/// </summary>
public static class SupportGemProcessor
{
    // Debug settings
    private static bool enableSupportGemProcessorDebugLogging = false;
    
    // Object pooling for zero-GC operation
    private static readonly Stack<List<float>> _floatListPool = new Stack<List<float>>();
    private static readonly object _poolLock = new object();
    private const int INITIAL_POOL_SIZE = 20;
    private const int LIST_INITIAL_CAPACITY = 4;

    // Pre-sorted priority order for LINQ elimination (MUST match enum values in descending order!)
    private static readonly EffectPriority[] _sortedPriorities = {
        EffectPriority.StatusEffectModifiers,      // 100
        EffectPriority.AutonomousSupport,          // 90  
        EffectPriority.Penetration,                // 85
        EffectPriority.SpellEcho,                  // 80
        EffectPriority.DamageConversion,           // 75
        EffectPriority.SpecialDamageMechanics,     // 74  
        EffectPriority.DamageMultipliers,          // 70
        EffectPriority.CooldownMultipliers,        // 69
        EffectPriority.ManaCostMultipliers,        // 68
        EffectPriority.AttackSpeedMultipliers,     // 67
        EffectPriority.CritMultipliers,            // 66
        EffectPriority.TypeDamageModifiers,        // 60
        EffectPriority.AdditiveModifiers,          // 50
        EffectPriority.ProjectileEffects,          // 30
        EffectPriority.AreaEffects,                // 29
        EffectPriority.ChainEffects,               // 28
        EffectPriority.ForkEffects,                // 27
        EffectPriority.PierceEffects               // 26
    };

    // Reusable priority buffer for zero-GC operation
    private static readonly EffectPriority[] _priorityBuffer = new EffectPriority[19]; // Max possible priorities

    static SupportGemProcessor()
    {
        // Pre-populate the float list pool
        for (int i = 0; i < INITIAL_POOL_SIZE; i++)
        {
            _floatListPool.Push(new List<float>(LIST_INITIAL_CAPACITY));
        }
    }

    /// <summary>
    /// Get a pooled List<float> for zero-GC operation
    /// </summary>
    private static List<float> GetPooledFloatList()
    {
        lock (_poolLock)
        {
            if (_floatListPool.Count > 0)
            {
                var list = _floatListPool.Pop();
                list.Clear(); // Ensure it's clean
                return list;
            }
        }

        // Pool exhausted - create new list (rare case)
        return new List<float>(LIST_INITIAL_CAPACITY);
    }

    /// <summary>
    /// Return a List<float> to the pool for reuse
    /// </summary>
    private static void ReturnPooledFloatList(List<float> list)
    {
        if (list == null) return;

        lock (_poolLock)
        {
            if (_floatListPool.Count < INITIAL_POOL_SIZE * 2) // Prevent unlimited growth
            {
                list.Clear();
                _floatListPool.Push(list);
            }
        }
    }
    /// <summary>
    /// Priority order for support gem effect application (higher number = higher priority)
    /// This ensures consistent behavior regardless of socket order.
    /// </summary>
    public enum EffectPriority
    {
        // Status effect modifiers (applied first, as base modifiers)
        StatusEffectModifiers = 100,
        
        // Autonomous support (highest priority - must be checked first for early returns)
        AutonomousSupport = 90,
        
        // Penetration (high priority - affects damage calculations before other modifiers)
        Penetration = 85,
        
        // Spell echo (high priority - affects casting mechanics)
        SpellEcho = 80,
        
        // Damage conversion (must be processed before type-specific modifiers)
        DamageConversion = 75,

        // Special damage mechanics (processed after conversion but before multipliers)
        SpecialDamageMechanics = 74,
        
        // Basic multipliers (applied in specific order to avoid compounding issues)
        DamageMultipliers = 70,
        CooldownMultipliers = 69,
        ManaCostMultipliers = 68,
        AttackSpeedMultipliers = 67,
        CritMultipliers = 66,
        
        // Type-specific damage modifiers (after conversion)
        TypeDamageModifiers = 60,
        
        // Additive modifiers (order-independent, but applied after multipliers)
        AdditiveModifiers = 50,
        
        // Projectile modifiers (applied last, as they affect final projectile behavior)
        ProjectileEffects = 30,
        AreaEffects = 29,
        ChainEffects = 28,
        ForkEffects = 27,
        PierceEffects = 26
    }

    /// <summary>
    /// Aggregated support gem effects structure for order-independent processing
    /// </summary>
    public struct AggregatedEffects
    {
        // Multiplicative effects (order-dependent - need special handling)
        public List<float> damageMoreMultipliers;
        public List<float> cooldownMultipliers;
        public List<float> manaCostMultipliers;
        public List<float> attackSpeedMultipliers;
        public List<float> critMultipliers;
        
        // Additive effects (order-independent)
        public float totalDamageIncrease;
        public float totalCritChanceIncrease;
        public int totalExtraProjectiles;
        
        // Boolean effects (precedence-based)
        public bool hasPierce;
        public bool hasChain;
        public bool hasFork;
        public bool hasAreaDamage;
        public bool hasSpellEcho;
        public bool hasMultipleProjectiles;
        public bool hasAutonomous;
        
        // Max value effects (take highest value)
        public int maxChainCount;
        public int maxForkCount;
        public float maxForkAngle;
        public float maxAreaRadius;
        public float maxProjectileSpreadAngle;
        public float maxProjectileLateralOffset;
        public bool useParallelProjectiles;
        
        // Spell echo effects (first valid effect wins)
        public int spellEchoCount;
        public float spellEchoDelay;
        public float spellEchoSpreadRadius;
        
        // Autonomous effects (first valid effect wins)
        public float autonomousRange;
        public float autonomousUpdateInterval;
        
        // Status effect multipliers (multiplicative combination)
        public List<float> igniteEffectivenessMultipliers;
        public List<float> igniteDurationMultipliers;
        public List<float> freezeEffectivenessMultipliers;
        public List<float> freezeDurationMultipliers;
        public List<float> bleedEffectivenessMultipliers;
        public List<float> bleedDurationMultipliers;
        public List<float> shockEffectivenessMultipliers;
        public List<float> shockRangeMultipliers;
        
        // Type-specific damage modifiers
        public float totalPhysicalIncreased;
        public float totalFireIncreased;
        public float totalIceIncreased;
        public float totalLightningIncreased;
        public float totalElementalIncreased;
        
        // Type-specific more multipliers
        public List<float> physicalMoreMultipliers;
        public List<float> fireMoreMultipliers;
        public List<float> iceMoreMultipliers;
        public List<float> lightningMoreMultipliers;
        public List<float> elementalMoreMultipliers;
        
        // Damage conversion
        public bool hasConversion;
        public DamageType conversionFromType;
        public DamageType conversionToType;
        public float conversionPercent;
        public string conversionSourceName;

        // Special damage mechanics
        public bool preventsElementalDamage;
        public bool hasRuthlessHits;
        public int ruthlessHitInterval;
        public float ruthlessDamageMultiplier;

        // Penetration stats
        public float totalArmorPenetrationPercent;
        public int totalFlatArmorPenetration;
        public float totalFirePenetration;
        public float totalColdPenetration;
        public float totalLightningPenetration;

        /// <summary>
        /// Initialize with pooled lists for zero-GC operation
        /// </summary>
        public void InitializePooled()
        {
            damageMoreMultipliers = GetPooledFloatList();
            cooldownMultipliers = GetPooledFloatList();
            manaCostMultipliers = GetPooledFloatList();
            attackSpeedMultipliers = GetPooledFloatList();
            critMultipliers = GetPooledFloatList();

            igniteEffectivenessMultipliers = GetPooledFloatList();
            igniteDurationMultipliers = GetPooledFloatList();
            freezeEffectivenessMultipliers = GetPooledFloatList();
            freezeDurationMultipliers = GetPooledFloatList();
            bleedEffectivenessMultipliers = GetPooledFloatList();
            bleedDurationMultipliers = GetPooledFloatList();
            shockEffectivenessMultipliers = GetPooledFloatList();
            shockRangeMultipliers = GetPooledFloatList();
            
            // Initialize type-specific multiplier lists
            physicalMoreMultipliers = GetPooledFloatList();
            fireMoreMultipliers = GetPooledFloatList();
            iceMoreMultipliers = GetPooledFloatList();
            lightningMoreMultipliers = GetPooledFloatList();
            elementalMoreMultipliers = GetPooledFloatList();

            // Initialize default values
            totalDamageIncrease = 0f;
            totalCritChanceIncrease = 0f;
            totalExtraProjectiles = 0;

            hasPierce = false;
            hasChain = false;
            hasFork = false;
            hasAreaDamage = false;
            hasSpellEcho = false;
            hasMultipleProjectiles = false;
            hasAutonomous = false;

            maxChainCount = 0;
            maxForkCount = 0;
            maxForkAngle = 0f;
            maxAreaRadius = 0f;
            maxProjectileSpreadAngle = 0f;
            maxProjectileLateralOffset = 0f;
            useParallelProjectiles = false;

            spellEchoCount = 0;
            spellEchoDelay = 0f;
            spellEchoSpreadRadius = 0f;

            autonomousRange = 0f;
            autonomousUpdateInterval = 0f;
            
            // Initialize type-specific increased modifiers
            totalPhysicalIncreased = 0f;
            totalFireIncreased = 0f;
            totalIceIncreased = 0f;
            totalLightningIncreased = 0f;
            totalElementalIncreased = 0f;
            
            // Initialize conversion
            hasConversion = false;
            conversionFromType = DamageType.Physical;
            conversionToType = DamageType.Physical;
            conversionPercent = 0f;
            conversionSourceName = string.Empty;
            
            // Initialize penetration stats
            totalArmorPenetrationPercent = 0f;
            totalFlatArmorPenetration = 0;
            totalFirePenetration = 0f;
            totalColdPenetration = 0f;
            totalLightningPenetration = 0f;
        }

        /// <summary>
        /// Legacy Initialize method for backward compatibility
        /// </summary>
        public void Initialize()
        {
            damageMoreMultipliers = new List<float>();
            cooldownMultipliers = new List<float>();
            manaCostMultipliers = new List<float>();
            attackSpeedMultipliers = new List<float>();
            critMultipliers = new List<float>();

            igniteEffectivenessMultipliers = new List<float>();
            igniteDurationMultipliers = new List<float>();
            freezeEffectivenessMultipliers = new List<float>();
            freezeDurationMultipliers = new List<float>();
            bleedEffectivenessMultipliers = new List<float>();
            bleedDurationMultipliers = new List<float>();
            shockEffectivenessMultipliers = new List<float>();
            shockRangeMultipliers = new List<float>();
            
            // Initialize type-specific multiplier lists
            physicalMoreMultipliers = new List<float>();
            fireMoreMultipliers = new List<float>();
            iceMoreMultipliers = new List<float>();
            lightningMoreMultipliers = new List<float>();
            elementalMoreMultipliers = new List<float>();

            // Initialize default values
            totalDamageIncrease = 0f;
            totalCritChanceIncrease = 0f;
            totalExtraProjectiles = 0;

            hasPierce = false;
            hasChain = false;
            hasFork = false;
            hasAreaDamage = false;
            hasSpellEcho = false;
            hasMultipleProjectiles = false;
            hasAutonomous = false;

            maxChainCount = 0;
            maxForkCount = 0;
            maxForkAngle = 0f;
            maxAreaRadius = 0f;
            maxProjectileSpreadAngle = 0f;
            maxProjectileLateralOffset = 0f;
            useParallelProjectiles = false;

            spellEchoCount = 0;
            spellEchoDelay = 0f;
            spellEchoSpreadRadius = 0f;

            autonomousRange = 0f;
            autonomousUpdateInterval = 0f;
            
            // Initialize type-specific increased modifiers
            totalPhysicalIncreased = 0f;
            totalFireIncreased = 0f;
            totalIceIncreased = 0f;
            totalLightningIncreased = 0f;
            totalElementalIncreased = 0f;
            
            // Initialize conversion
            hasConversion = false;
            conversionFromType = DamageType.Physical;
            conversionToType = DamageType.Physical;
            conversionPercent = 0f;
            conversionSourceName = string.Empty;
            
            // Initialize penetration stats
            totalArmorPenetrationPercent = 0f;
            totalFlatArmorPenetration = 0;
            totalFirePenetration = 0f;
            totalColdPenetration = 0f;
            totalLightningPenetration = 0f;
        }

        /// <summary>
        /// Return all pooled lists back to the pool for reuse
        /// </summary>
        public void ReturnToPool()
        {
            ReturnPooledFloatList(damageMoreMultipliers);
            ReturnPooledFloatList(cooldownMultipliers);
            ReturnPooledFloatList(manaCostMultipliers);
            ReturnPooledFloatList(attackSpeedMultipliers);
            ReturnPooledFloatList(critMultipliers);

            ReturnPooledFloatList(igniteEffectivenessMultipliers);
            ReturnPooledFloatList(igniteDurationMultipliers);
            ReturnPooledFloatList(freezeEffectivenessMultipliers);
            ReturnPooledFloatList(freezeDurationMultipliers);
            ReturnPooledFloatList(bleedEffectivenessMultipliers);
            ReturnPooledFloatList(bleedDurationMultipliers);
            ReturnPooledFloatList(shockEffectivenessMultipliers);
            ReturnPooledFloatList(shockRangeMultipliers);
            
            // Return type-specific multiplier lists
            ReturnPooledFloatList(physicalMoreMultipliers);
            ReturnPooledFloatList(fireMoreMultipliers);
            ReturnPooledFloatList(iceMoreMultipliers);
            ReturnPooledFloatList(lightningMoreMultipliers);
            ReturnPooledFloatList(elementalMoreMultipliers);

            // Clear references
            damageMoreMultipliers = null;
            cooldownMultipliers = null;
            manaCostMultipliers = null;
            attackSpeedMultipliers = null;
            critMultipliers = null;
            igniteEffectivenessMultipliers = null;
            igniteDurationMultipliers = null;
            freezeEffectivenessMultipliers = null;
            freezeDurationMultipliers = null;
            bleedEffectivenessMultipliers = null;
            bleedDurationMultipliers = null;
            shockEffectivenessMultipliers = null;
            shockRangeMultipliers = null;
        }
    }

    /// <summary>
    /// Process support gems in order-independent manner by collecting and aggregating effects
    /// NOTE: This version is used ONLY for UI tooltips. For gameplay, use ProcessSupportGemsZeroGC instead.
    /// </summary>
    public static AggregatedEffects ProcessSupportGemsForUI(List<GemInstance> supportGems)
    {
        var effects = new AggregatedEffects();
        effects.Initialize();
        
        if (supportGems == null || supportGems.Count == 0)
            return effects;

        // Group support gems by effect priority for ordered processing
        var prioritizedEffects = new Dictionary<EffectPriority, List<GemInstance>>();
        
        foreach (var supportInstance in supportGems)
        {
            if (supportInstance?.gemDataTemplate is SupportGemData supportGem)
            {
                // Classify gem into priority categories
                var priorities = GetEffectPriorities(supportInstance);
                foreach (var priority in priorities)
                {
                    if (!prioritizedEffects.ContainsKey(priority))
                        prioritizedEffects[priority] = new List<GemInstance>();
                    prioritizedEffects[priority].Add(supportInstance);
                }
            }
        }

        // Process effects in priority order
        var sortedPriorities = prioritizedEffects.Keys.OrderByDescending(p => (int)p);
        
        foreach (var priority in sortedPriorities)
        {
            var gemsWithThisPriority = prioritizedEffects[priority];
            
            switch (priority)
            {
                case EffectPriority.AutonomousSupport:
                    ProcessAutonomousEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.Penetration:
                    ProcessPenetrationEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.SpellEcho:
                    ProcessSpellEchoEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.DamageConversion:
                    ProcessDamageConversion(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.SpecialDamageMechanics:
                    ProcessSpecialDamageMechanics(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.DamageMultipliers:
                    ProcessDamageMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.CooldownMultipliers:
                    ProcessCooldownMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.ManaCostMultipliers:
                    ProcessManaCostMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.AttackSpeedMultipliers:
                    ProcessAttackSpeedMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.CritMultipliers:
                    ProcessCritMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.TypeDamageModifiers:
                    ProcessTypeDamageModifiers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.AdditiveModifiers:
                    ProcessAdditiveModifiers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.ProjectileEffects:
                    ProcessProjectileEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.AreaEffects:
                    ProcessAreaEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.ChainEffects:
                    ProcessChainEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.ForkEffects:
                    ProcessForkEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.PierceEffects:
                    ProcessPierceEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.StatusEffectModifiers:
                    ProcessStatusEffectModifiers(gemsWithThisPriority, ref effects);
                    break;
            }
        }

        return effects;
    }


    /// <summary>
    /// Fully zero-GC version of ProcessSupportGems - eliminates all allocations
    /// Uses pre-sorted priority arrays and fixed-size buckets instead of Dictionary and LINQ
    /// Caller must call ReturnToPool() on the returned effects when done
    /// </summary>
    public static AggregatedEffects ProcessSupportGemsZeroGC(List<GemInstance> supportGems)
    {
        var effects = new AggregatedEffects();
        effects.InitializePooled(); // Use pooled lists

        if (supportGems == null || supportGems.Count == 0)
            return effects;

        // Use fixed-size arrays instead of Dictionary for priority grouping
        // Pre-allocate buckets for each priority (max 16 gems per priority should be enough)
        var priorityBuckets = new List<GemInstance>[_sortedPriorities.Length];
        for (int i = 0; i < priorityBuckets.Length; i++)
        {
            priorityBuckets[i] = new List<GemInstance>(4); // Small initial capacity
        }

        // Group gems by priority using zero-GC priority detection
        foreach (var supportInstance in supportGems)
        {
            if (supportInstance?.gemDataTemplate is SupportGemData supportGem)
            {
                // Get priorities using zero-GC method
                int priorityCount = GetEffectPrioritiesZeroGC(supportInstance, _priorityBuffer);

                // Add to appropriate buckets
                for (int i = 0; i < priorityCount; i++)
                {
                    var priority = _priorityBuffer[i];

                    // Find bucket index for this priority
                    for (int bucketIndex = 0; bucketIndex < _sortedPriorities.Length; bucketIndex++)
                    {
                        if (_sortedPriorities[bucketIndex] == priority)
                        {
                            priorityBuckets[bucketIndex].Add(supportInstance);
                            break;
                        }
                    }
                }
            }
        }

        // Process effects in pre-sorted priority order (no LINQ)
        for (int priorityIndex = 0; priorityIndex < _sortedPriorities.Length; priorityIndex++)
        {
            var priority = _sortedPriorities[priorityIndex];
            var gemsWithThisPriority = priorityBuckets[priorityIndex];

            if (gemsWithThisPriority.Count == 0) continue;

            switch (priority)
            {
                case EffectPriority.AutonomousSupport:
                    ProcessAutonomousEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.Penetration:
                    ProcessPenetrationEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.SpellEcho:
                    ProcessSpellEchoEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.DamageConversion:
                    ProcessDamageConversion(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.SpecialDamageMechanics:
                    if (enableSupportGemProcessorDebugLogging)
                        UnityEngine.Debug.Log($"[ProcessSupportGemsZeroGC] Processing SpecialDamageMechanics with {gemsWithThisPriority.Count} gems");
                    ProcessSpecialDamageMechanics(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.DamageMultipliers:
                    ProcessDamageMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.CooldownMultipliers:
                    ProcessCooldownMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.ManaCostMultipliers:
                    ProcessManaCostMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.AttackSpeedMultipliers:
                    ProcessAttackSpeedMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.CritMultipliers:
                    ProcessCritMultipliers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.TypeDamageModifiers:
                    ProcessTypeDamageModifiers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.AdditiveModifiers:
                    ProcessAdditiveModifiers(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.ProjectileEffects:
                    ProcessProjectileEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.AreaEffects:
                    ProcessAreaEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.ChainEffects:
                    ProcessChainEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.ForkEffects:
                    ProcessForkEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.PierceEffects:
                    ProcessPierceEffects(gemsWithThisPriority, ref effects);
                    break;
                case EffectPriority.StatusEffectModifiers:
                    ProcessStatusEffectModifiers(gemsWithThisPriority, ref effects);
                    break;
            }
        }

        return effects;
    }

    /// <summary>
    /// Zero-GC version: Determine which effect priorities a support gem contributes to
    /// Returns count of priorities written to the buffer
    /// </summary>
    private static int GetEffectPrioritiesZeroGC(GemInstance gem, EffectPriority[] buffer)
    {
        int count = 0;

        if (!(gem?.gemDataTemplate is SupportGemData supportGem))
            return count;

        if (supportGem.addsAutonomous)
            buffer[count++] = EffectPriority.AutonomousSupport;

        if (HasPenetrationStatsGem(supportGem))
            buffer[count++] = EffectPriority.Penetration;

        if (supportGem.addsSpellEcho)
            buffer[count++] = EffectPriority.SpellEcho;

        if (supportGem.isConversionGem)
            buffer[count++] = EffectPriority.DamageConversion;

        if (supportGem.preventsElementalDamage || supportGem.addsRuthlessHits)
        {
            if (enableSupportGemProcessorDebugLogging)
                UnityEngine.Debug.Log($"[SupportGemProcessor-ZeroGC] Detected SpecialDamageMechanics gem: {supportGem.gemName}, preventsElementalDamage={supportGem.preventsElementalDamage}");
            buffer[count++] = EffectPriority.SpecialDamageMechanics;
        }

        if (HasDamageMultipliersIncludingRandom(gem))
            buffer[count++] = EffectPriority.DamageMultipliers;

        if (supportGem.cooldownMultiplier != 1f)
            buffer[count++] = EffectPriority.CooldownMultipliers;

        if (supportGem.manaCostMultiplier != 1f)
            buffer[count++] = EffectPriority.ManaCostMultipliers;

        if (supportGem.attackSpeedMultiplier != 1f)
            buffer[count++] = EffectPriority.AttackSpeedMultipliers;

        if (supportGem.critMultiplierModifier != 1f)
            buffer[count++] = EffectPriority.CritMultipliers;

        // Check if any type damage modifiers are present (including random modifiers)
        if (HasTypeDamageModifiersIncludingRandom(gem))
            buffer[count++] = EffectPriority.TypeDamageModifiers;

        if (supportGem.damageIncreased != 0f || supportGem.addedCritChance != 0f)
            buffer[count++] = EffectPriority.AdditiveModifiers;

        if (supportGem.addsMultipleProjectiles)
            buffer[count++] = EffectPriority.ProjectileEffects;

        if (supportGem.addsAreaDamage)
            buffer[count++] = EffectPriority.AreaEffects;

        if (supportGem.addsChain)
            buffer[count++] = EffectPriority.ChainEffects;

        if (supportGem.addsFork)
            buffer[count++] = EffectPriority.ForkEffects;

        if (supportGem.addsPierce)
            buffer[count++] = EffectPriority.PierceEffects;

        // Check if any status effect modifiers are present
        if (HasStatusEffectModifiers(supportGem))
            buffer[count++] = EffectPriority.StatusEffectModifiers;

        return count;
    }

    /// <summary>
    /// Legacy version: Determine which effect priorities a support gem contributes to
    /// </summary>
    private static List<EffectPriority> GetEffectPriorities(GemInstance gem)
    {
        var priorities = new List<EffectPriority>();

        if (!(gem?.gemDataTemplate is SupportGemData supportGem))
            return priorities;

        if (supportGem.addsAutonomous)
            priorities.Add(EffectPriority.AutonomousSupport);

        if (HasPenetrationStatsGem(supportGem))
            priorities.Add(EffectPriority.Penetration);

        if (supportGem.addsSpellEcho)
            priorities.Add(EffectPriority.SpellEcho);

        if (supportGem.isConversionGem)
            priorities.Add(EffectPriority.DamageConversion);

        if (supportGem.preventsElementalDamage || supportGem.addsRuthlessHits)
            priorities.Add(EffectPriority.SpecialDamageMechanics);

        if (HasDamageMultipliersIncludingRandom(gem))
            priorities.Add(EffectPriority.DamageMultipliers);

        if (supportGem.cooldownMultiplier != 1f)
            priorities.Add(EffectPriority.CooldownMultipliers);

        if (supportGem.manaCostMultiplier != 1f)
            priorities.Add(EffectPriority.ManaCostMultipliers);

        if (supportGem.attackSpeedMultiplier != 1f)
            priorities.Add(EffectPriority.AttackSpeedMultipliers);

        if (supportGem.critMultiplierModifier != 1f)
            priorities.Add(EffectPriority.CritMultipliers);

        // Check if any type damage modifiers are present (including random modifiers)
        if (HasTypeDamageModifiersIncludingRandom(gem))
            priorities.Add(EffectPriority.TypeDamageModifiers);

        if (supportGem.damageIncreased != 0f || supportGem.addedCritChance != 0f)
            priorities.Add(EffectPriority.AdditiveModifiers);

        if (supportGem.addsMultipleProjectiles)
            priorities.Add(EffectPriority.ProjectileEffects);

        if (supportGem.addsAreaDamage)
            priorities.Add(EffectPriority.AreaEffects);

        if (supportGem.addsChain)
            priorities.Add(EffectPriority.ChainEffects);

        if (supportGem.addsFork)
            priorities.Add(EffectPriority.ForkEffects);

        if (supportGem.addsPierce)
            priorities.Add(EffectPriority.PierceEffects);

        // Check if any status effect modifiers are present
        if (HasStatusEffectModifiers(supportGem))
            priorities.Add(EffectPriority.StatusEffectModifiers);

        return priorities;
    }

    private static bool HasStatusEffectModifiers(SupportGemData supportGem)
    {
        return supportGem.igniteEffectivenessMultiplier != 1f ||
               supportGem.igniteDurationMultiplier != 1f ||
               supportGem.freezeEffectivenessMultiplier != 1f ||
               supportGem.freezeDurationMultiplier != 1f ||
               supportGem.bleedEffectivenessMultiplier != 1f ||
               supportGem.bleedDurationMultiplier != 1f ||
               supportGem.shockEffectivenessMultiplier != 1f ||
               supportGem.shockRangeMultiplier != 1f;
    }

    // Effect processing methods
    private static void ProcessAutonomousEffects(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        // For autonomous effects, use the first valid gem found (deterministic order)
        var sortedGems = gems.OrderBy(g => g.instanceId).ToList();
        foreach (var gem in sortedGems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem && supportGem.addsAutonomous)
            {
                effects.hasAutonomous = true;
                effects.autonomousRange = supportGem.autonomousRange;
                effects.autonomousUpdateInterval = supportGem.autonomousUpdateInterval;
                break; // Use first gem found
            }
        }
    }

    private static void ProcessSpellEchoEffects(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        // For spell echo, use the first valid gem found (deterministic order)
        var sortedGems = gems.OrderBy(g => g.instanceId).ToList();
        foreach (var gem in sortedGems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem && supportGem.addsSpellEcho)
            {
                effects.hasSpellEcho = true;
                effects.spellEchoCount = gem.GetSpellEchoCount();
                effects.spellEchoDelay = supportGem.echoDelay;
                effects.spellEchoSpreadRadius = supportGem.echoSpreadRadius;
                break; // Use first gem found
            }
        }
    }

    private static void ProcessDamageMultipliers(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        // Sort by instance ID for deterministic order, then collect all multipliers
        var sortedGems = gems.OrderBy(g => g.instanceId).ToList();
        foreach (var gem in sortedGems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem)
            {
                var multiplier = gem.GetSupportDamageMultiplier();
                effects.damageMoreMultipliers.Add(multiplier);
            }
        }
    }

    private static void ProcessCooldownMultipliers(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        var sortedGems = gems.OrderBy(g => g.instanceId).ToList();
        foreach (var gem in sortedGems)
        {
            var multiplier = gem.GetSupportCooldownMultiplier();
            effects.cooldownMultipliers.Add(multiplier);
        }
    }

    private static void ProcessManaCostMultipliers(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        var sortedGems = gems.OrderBy(g => g.instanceId).ToList();
        foreach (var gem in sortedGems)
        {
            var multiplier = gem.GetSupportManaCostMultiplier();
            effects.manaCostMultipliers.Add(multiplier);
        }
    }

    private static void ProcessAttackSpeedMultipliers(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        var sortedGems = gems.OrderBy(g => g.instanceId).ToList();
        foreach (var gem in sortedGems)
        {
            var multiplier = gem.GetSupportAttackSpeedMultiplier();
            effects.attackSpeedMultipliers.Add(multiplier);
        }
    }

    private static void ProcessCritMultipliers(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        var sortedGems = gems.OrderBy(g => g.instanceId).ToList();
        foreach (var gem in sortedGems)
        {
            var multiplier = gem.GetSupportCritMultiplierModifier();
            effects.critMultipliers.Add(multiplier);
        }
    }

    private static void ProcessAdditiveModifiers(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        foreach (var gem in gems)
        {
            effects.totalDamageIncrease += gem.GetSupportIncreasedDamage();
            effects.totalCritChanceIncrease += gem.GetSupportCritChanceBonus();
        }
    }

    private static void ProcessProjectileEffects(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem && supportGem.addsMultipleProjectiles)
            {
                effects.hasMultipleProjectiles = true;
                effects.totalExtraProjectiles += gem.GetExtraProjectiles();
                
                // Use max values for spread settings
                effects.maxProjectileSpreadAngle = Mathf.Max(effects.maxProjectileSpreadAngle, supportGem.projectileSpreadAngle);
                effects.maxProjectileLateralOffset = Mathf.Max(effects.maxProjectileLateralOffset, supportGem.projectileLateralOffset);
                
                // Use parallel projectiles if any gem specifies it
                if (supportGem.useParallelProjectiles)
                    effects.useParallelProjectiles = true;
            }
        }
    }

    private static void ProcessAreaEffects(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem && supportGem.addsAreaDamage)
            {
                effects.hasAreaDamage = true;
                // Note: Area radius is now calculated as percentage increases in individual systems
                // No longer using a single maxAreaRadius value
            }
        }
    }

    private static void ProcessChainEffects(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem && supportGem.addsChain)
            {
                effects.hasChain = true;
                effects.maxChainCount = Mathf.Max(effects.maxChainCount, gem.GetChainCount());
            }
        }
    }

    private static void ProcessForkEffects(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem && supportGem.addsFork)
            {
                effects.hasFork = true;
                effects.maxForkCount = Mathf.Max(effects.maxForkCount, gem.GetForkCount());
                effects.maxForkAngle = Mathf.Max(effects.maxForkAngle, supportGem.forkAngle);
            }
        }
    }

    private static void ProcessPierceEffects(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem && supportGem.addsPierce)
            {
                effects.hasPierce = true;
            }
        }
    }
    
    private static void ProcessDamageConversion(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        // Only process the first conversion gem found (since only one is allowed)
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem && supportGem.isConversionGem)
            {
                effects.hasConversion = true;
                effects.conversionFromType = supportGem.convertFromType;
                effects.conversionToType = supportGem.convertToType;
                effects.conversionPercent = supportGem.conversionPercent;
                effects.conversionSourceName = supportGem.gemName;
                break; // Only one conversion allowed
            }
        }
    }
    
    private static void ProcessTypeDamageModifiers(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem)
            {
                // Accumulate increased modifiers with rarity scaling
                effects.totalPhysicalIncreased += gem.GetSupportPhysicalDamageIncreased();
                effects.totalFireIncreased += gem.GetSupportFireDamageIncreased();
                effects.totalIceIncreased += gem.GetSupportIceDamageIncreased();
                effects.totalLightningIncreased += gem.GetSupportLightningDamageIncreased();
                effects.totalElementalIncreased += gem.GetSupportElementalDamageIncreased();
                
                // Add more multipliers to lists with rarity scaling
                float physicalMore = gem.GetSupportPhysicalDamageMore();
                if (physicalMore != 1f)
                    effects.physicalMoreMultipliers.Add(physicalMore);
                    
                float fireMore = gem.GetSupportFireDamageMore();
                if (fireMore != 1f)
                    effects.fireMoreMultipliers.Add(fireMore);
                    
                float iceMore = gem.GetSupportIceDamageMore();
                if (iceMore != 1f)
                    effects.iceMoreMultipliers.Add(iceMore);
                    
                float lightningMore = gem.GetSupportLightningDamageMore();
                if (lightningMore != 1f)
                    effects.lightningMoreMultipliers.Add(lightningMore);
                    
                float elementalMore = gem.GetSupportElementalDamageMore();
                if (elementalMore != 1f)
                    effects.elementalMoreMultipliers.Add(elementalMore);
            }
        }
    }

    private static void ProcessSpecialDamageMechanics(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        if (enableSupportGemProcessorDebugLogging)
            UnityEngine.Debug.Log($"[ProcessSpecialDamageMechanics] Processing {gems.Count} gems for special damage mechanics");
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem)
            {
                if (enableSupportGemProcessorDebugLogging)
                    UnityEngine.Debug.Log($"[ProcessSpecialDamageMechanics] Checking gem: {supportGem.gemName}, preventsElementalDamage: {supportGem.preventsElementalDamage}");
                
                // Elemental damage prevention (Brutality Support)
                if (supportGem.preventsElementalDamage)
                {
                    if (enableSupportGemProcessorDebugLogging)
                        UnityEngine.Debug.Log($"[SupportGemProcessor] Brutality Support detected! Setting preventsElementalDamage=true");
                    effects.preventsElementalDamage = true;
                }

                // Ruthless hits (Ruthless Support) - only one ruthless gem allowed
                if (supportGem.addsRuthlessHits && !effects.hasRuthlessHits)
                {
                    effects.hasRuthlessHits = true;
                    effects.ruthlessHitInterval = supportGem.ruthlessHitInterval;
                    effects.ruthlessDamageMultiplier = supportGem.ruthlessDamageMultiplier;
                }
            }
        }
    }

    private static void ProcessStatusEffectModifiers(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        var sortedGems = gems.OrderBy(g => g.instanceId).ToList();
        foreach (var gem in sortedGems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem)
            {
                if (supportGem.igniteEffectivenessMultiplier != 1f)
                    effects.igniteEffectivenessMultipliers.Add(supportGem.igniteEffectivenessMultiplier);
                if (supportGem.igniteDurationMultiplier != 1f)
                    effects.igniteDurationMultipliers.Add(supportGem.igniteDurationMultiplier);
                if (supportGem.freezeEffectivenessMultiplier != 1f)
                    effects.freezeEffectivenessMultipliers.Add(supportGem.freezeEffectivenessMultiplier);
                if (supportGem.freezeDurationMultiplier != 1f)
                    effects.freezeDurationMultipliers.Add(supportGem.freezeDurationMultiplier);
                if (supportGem.bleedEffectivenessMultiplier != 1f)
                    effects.bleedEffectivenessMultipliers.Add(supportGem.bleedEffectivenessMultiplier);
                if (supportGem.bleedDurationMultiplier != 1f)
                    effects.bleedDurationMultipliers.Add(supportGem.bleedDurationMultiplier);
                if (supportGem.shockEffectivenessMultiplier != 1f)
                    effects.shockEffectivenessMultipliers.Add(supportGem.shockEffectivenessMultiplier);
                if (supportGem.shockRangeMultiplier != 1f)
                    effects.shockRangeMultipliers.Add(supportGem.shockRangeMultiplier);
            }
        }
    }

    /// <summary>
    /// Check if a gem instance has type-specific damage modifiers (base properties or random modifiers)
    /// </summary>
    private static bool HasTypeDamageModifiersIncludingRandom(GemInstance gem)
    {
        if (gem?.gemDataTemplate is SupportGemData supportGem)
        {
            // Check base properties
            if (supportGem.physicalDamageIncreased != 0f || supportGem.physicalDamageMore != 1f ||
                supportGem.fireDamageIncreased != 0f || supportGem.fireDamageMore != 1f ||
                supportGem.iceDamageIncreased != 0f || supportGem.iceDamageMore != 1f ||
                supportGem.lightningDamageIncreased != 0f || supportGem.lightningDamageMore != 1f ||
                supportGem.elementalDamageIncreased != 0f || supportGem.elementalDamageMore != 1f)
            {
                return true;
            }

            // Check random modifiers that affect type-specific damage
            if (gem.GetRandomModifierBonus(SupportGemModifierType.ElementalDamageMultiplier) != 0f)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Check if a gem instance has generic damage multipliers (base properties or random modifiers)
    /// </summary>
    private static bool HasDamageMultipliersIncludingRandom(GemInstance gem)
    {
        if (gem?.gemDataTemplate is SupportGemData supportGem)
        {
            // Check base properties
            if (supportGem.damageMore != 1f)
            {
                return true;
            }

            // Check random modifiers that affect generic damage multipliers
            if (gem.GetRandomModifierBonus(SupportGemModifierType.DamageMultiplier) != 0f)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Check if a support gem has penetration stats
    /// </summary>
    private static bool HasPenetrationStatsGem(SupportGemData supportGem)
    {
        return supportGem.armorPenetrationPercent > 0f || supportGem.flatArmorPenetration > 0 ||
               supportGem.firePenetration > 0f || supportGem.coldPenetration > 0f ||
               supportGem.lightningPenetration > 0f || supportGem.elementalPenetration > 0f;
    }

    /// <summary>
    /// Process penetration effects from support gems
    /// </summary>
    private static void ProcessPenetrationEffects(List<GemInstance> gems, ref AggregatedEffects effects)
    {
        foreach (var gem in gems)
        {
            if (gem?.gemDataTemplate is SupportGemData supportGem)
            {
                // Accumulate armor penetration
                effects.totalArmorPenetrationPercent += gem.GetArmorPenetrationPercent();
                effects.totalFlatArmorPenetration += gem.GetFlatArmorPenetration();
                
                // Accumulate elemental penetration (including elemental penetration applied to all types)
                float elementalPenetration = gem.GetElementalPenetration();
                effects.totalFirePenetration += gem.GetFirePenetration() + elementalPenetration;
                effects.totalColdPenetration += gem.GetColdPenetration() + elementalPenetration;
                effects.totalLightningPenetration += gem.GetLightningPenetration() + elementalPenetration;
            }
        }
    }

    /// <summary>
    /// Calculate final multiplied value from a list of multipliers using deterministic order
    /// </summary>
    public static float CalculateMultipliedValue(float baseValue, List<float> multipliers)
    {
        if (multipliers == null || multipliers.Count == 0)
            return baseValue;

        // Sort multipliers for deterministic order (largest first to minimize floating point errors)
        var sortedMultipliers = multipliers.OrderByDescending(m => m).ToList();
        
        float result = baseValue;
        foreach (var multiplier in sortedMultipliers)
        {
            result *= multiplier;
        }
        return result;
    }
}