%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1643882132390136886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8236991014535762667}
  - component: {fileID: 4056240904290507487}
  - component: {fileID: 4021381480158349460}
  - component: {fileID: 4491164869510236316}
  m_Layer: 5
  m_Name: UnlockGemSlot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8236991014535762667
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643882132390136886}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6367413116056685259}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4056240904290507487
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643882132390136886}
  m_CullTransparentMesh: 1
--- !u!114 &4021381480158349460
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643882132390136886}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Flexalon::Flexalon.FlexalonResult
  Parent: {fileID: 0}
  SiblingIndex: 0
  LayoutPosition: {x: -160, y: 160, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 37.5, y: 37.5, z: 0}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 37.5, y: 37.5, z: 0}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 37.5, y: 37.5, z: 0}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -160, y: 160, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 75, y: 75, z: 0}
  TransformPosition: {x: -160, y: 160, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 75, y: 75}
--- !u!114 &4491164869510236316
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643882132390136886}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 26ba90c991910a740a5e20cdd993f692, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::UnlockSlot
  layeredIconDisplay: {fileID: 4009261066025039697}
  borderImage: {fileID: 0}
  unlockedBorderColor: {r: 1, g: 1, b: 1, a: 1}
  backgroundImage: {fileID: 0}
  unlockedBackgroundColor: {r: 1, g: 1, b: 1, a: 0.2}
  enableTooltip: 1
--- !u!1 &2382107518437943926
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6367413116056685259}
  - component: {fileID: 7603325343908786823}
  - component: {fileID: 4344046567249743552}
  - component: {fileID: 7560114944630395931}
  m_Layer: 5
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6367413116056685259
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2382107518437943926}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.8, y: 0.8, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8963008684743221576}
  - {fileID: 4048135481161849001}
  m_Father: {fileID: 8236991014535762667}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7603325343908786823
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2382107518437943926}
  m_CullTransparentMesh: 1
--- !u!114 &4344046567249743552
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2382107518437943926}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: UnityEngine.UI::UnityEngine.UI.Image
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -9051276650871165711, guid: fefd47a80a7882d41acca61e7d30bae9, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &7560114944630395931
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2382107518437943926}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!1 &5406202140746615111
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8963008684743221576}
  - component: {fileID: 6531893980356164181}
  - component: {fileID: 6747520066301324573}
  m_Layer: 5
  m_Name: Frame
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &8963008684743221576
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5406202140746615111}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6367413116056685259}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 65, y: 65}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6531893980356164181
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5406202140746615111}
  m_CullTransparentMesh: 1
--- !u!114 &6747520066301324573
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5406202140746615111}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: UnityEngine.UI::UnityEngine.UI.Image
  m_Material: {fileID: 0}
  m_Color: {r: 0.5188679, g: 0.48705053, b: 0.48705053, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1001 &7156776931461426152
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6367413116056685259}
    m_Modifications:
    - target: {fileID: 2502463426711130870, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_Name
      value: LayeredIconDisplay
      objectReference: {fileID: 0}
    - target: {fileID: 2502463426711130870, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_SizeDelta.x
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_SizeDelta.y
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
--- !u!114 &4009261066025039697 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6121019722861255865, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
  m_PrefabInstance: {fileID: 7156776931461426152}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f7e2562abf164f14f9997f2493319967, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.UI.Components.LayeredIconDisplay
--- !u!224 &4048135481161849001 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 6592187346034717505, guid: a52c272e6b3980c4da6f2c285f40128e, type: 3}
  m_PrefabInstance: {fileID: 7156776931461426152}
  m_PrefabAsset: {fileID: 0}
