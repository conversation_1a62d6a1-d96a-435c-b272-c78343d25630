%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc7afdaae02bac047acf72aabe6a2a9c, type: 3}
  m_Name: Spark
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SkillGemData
  icon: {fileID: -1761832914240646678, guid: e2faaafcd899af140b2267d9a0846ef8, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Spark
  description: Launches sparking projectiles that travel in random directions. The
    projectiles pierce all targets and chain between enemies.
  rarity: 0
  requiresUnlock: 0
  unlockCondition: {fileID: 0}
  skillType: 2
  gemTags: 558
  skillPrefab: {fileID: 2340940985034927489, guid: 259e49f1798593445a2a0883b6db244f, type: 3}
  baseDamage: 1
  cooldown: 0.8
  manaCost: 12
  projectileSpeed: 5.1
  duration: 2
  waveAmplitude: 0.02
  waveFrequency: 5
  serpentineDelay: 0.2
  projectileCount: 1
  projectileDelay: 0.2
  orbitRadius: 2
  rotationSpeed: 180
  bladeCount: 3
  targetGroundPosition: 1
  attackSpeedMultiplier: 1.2
  projectileLayer: 8
  critChance: 6
  critMultiplier: 1.5
  damageType: 3
  ailmentChance: 7.5
  intrinsicProjectileCount: 4
  intrinsicSpreadAngle: 20
  intrinsicUseRandomSpread: 1
  intrinsicHasPierce: 1
  intrinsicHasChain: 0
  intrinsicChainCount: 2
  projectileSpeedVariation: 2
  serpentineAmplitudeVariation: 0
  serpentineFrequencyVariation: 0
  ignitePercent: 0.2
  igniteDuration: 4
  igniteTickInterval: 0.5
  freezeSlowAmount: 0.5
  freezeDuration: 2
  bleedPercent: 0.15
  bleedDuration: 6
  bleedTickInterval: 1
  shockChainDamage: 0.2
  shockChainRange: 4
  shockDuration: 3
