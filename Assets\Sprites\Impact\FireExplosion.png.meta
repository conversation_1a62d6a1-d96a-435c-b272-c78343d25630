fileFormatVersion: 2
guid: 5b9136edfe0d9db43acf7de01be5bd8f
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 8633881525917383573
    second: Spritesheet_0
  - first:
      213: -1771866121167276815
    second: Spritesheet_1
  - first:
      213: 2091829175261615561
    second: Spritesheet_2
  - first:
      213: 756911166660455246
    second: Spritesheet_3
  - first:
      213: 403005533434085257
    second: Spritesheet_4
  - first:
      213: 6961653080833796888
    second: Spritesheet_5
  - first:
      213: 6266472923241529454
    second: Spritesheet_6
  - first:
      213: -5282911695883833476
    second: Spritesheet_7
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Spritesheet_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59bfc19e085b1d770800000000000000
      internalID: 8633881525917383573
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Spritesheet_1
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f4764a86211967e0800000000000000
      internalID: -1771866121167276815
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Spritesheet_2
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9c5544a4d8ba70d10800000000000000
      internalID: 2091829175261615561
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Spritesheet_3
      rect:
        serializedVersion: 2
        x: 48
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4ba25f12b6118a00800000000000000
      internalID: 756911166660455246
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Spritesheet_4
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 98ffadeb563c79500800000000000000
      internalID: 403005533434085257
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Spritesheet_5
      rect:
        serializedVersion: 2
        x: 80
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 81b2a94b792cc9060800000000000000
      internalID: 6961653080833796888
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Spritesheet_6
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e604a682cebf6f650800000000000000
      internalID: 6266472923241529454
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 8f65468aa9f517f4fac9b9779419959d
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":7.0,"y":1.0},"gridSpriteSize":{"x":16.0,"y":16.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":2,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      Spritesheet_0: 8633881525917383573
      Spritesheet_1: -1771866121167276815
      Spritesheet_2: 2091829175261615561
      Spritesheet_3: 756911166660455246
      Spritesheet_4: 403005533434085257
      Spritesheet_5: 6961653080833796888
      Spritesheet_6: 6266472923241529454
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
