using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(Collider2D))]
public class Projectile : MonoBehaviour, ISpawnable
{
    [Title("Projectile Settings")]
    [SerializeField] private float defaultSpeed = 10f;
    [SerializeField] private float defaultLifetime = 2f;
    [SerializeField] private float baseDamage = 10f;
    
    [Title("Collision Settings")]
    [SerializeField, Tooltip("FirstHitOnly: Process only first collision (standard projectiles)\nAllHits: Process all collisions (rare)\nMaxHits: Process up to max limit (piercing)")]
    private HitProcessingMode hitProcessingMode = HitProcessingMode.FirstHitOnly;
    
    [SerializeField, Range(0.1f, 2f), Tooltip("Collision detection radius")]
    private float collisionRadius = 0.5f;
    
    [Title("Debug Settings")]
    [SerializeField, Tooltip("Enable debug logging for fork projectile status effect issues")]
    private bool enableForkDebugLogging = true;
    
    [Serial<PERSON><PERSON><PERSON>, Tooltip("Show collision debug info in scene view")]
    private bool showCollisionDebug = false;

    [Title("Particle Effects")]
    [SerializeField] private bool useImpactParticles = true;
    [ShowIf("useImpactParticles")]
    [SerializeField] private ParticleType impactParticleType = ParticleType.SparkImpact;
    [ShowIf("useImpactParticles")]
    [SerializeField] private int impactParticleCount = 10;
    
    [SerializeField] private bool useTrailParticles = false;
    [ShowIf("useTrailParticles")]
    [SerializeField] private ParticleType trailParticleType = ParticleType.FireTrail;
    [ShowIf("useTrailParticles")]
    [SerializeField] private float trailInterval = 0.1f;
    
    [SerializeField] private bool useDespawnParticles = true;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private ParticleType despawnParticleType = ParticleType.SmokeImpact;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private int despawnParticleCount = 5;
    
    [InfoBox("Optional: Assign a child transform for precise particle spawn position")]
    [SerializeField] private Transform particleSpawnPoint;
    
    // Runtime values
    public float speed { get; set; }
    public float lifetime { get; set; }
    public float damage { get; set; }
    public float critChance { get; set; }
    public float critMultiplier { get; set; }
    public DamageType damageType { get; set; } = DamageType.Physical;
    public float ailmentChance { get; set; } = 0f;

    // Gem data for status effect configuration
    public SkillGemData skillGemData { get; set; }
    public System.Collections.Generic.List<GemInstance> supportGems { get; set; }
    
    // Damage breakdown for type-specific damage
    public DamageBreakdown? damageBreakdown { get; set; }
    
    // Support gem effects
    private bool isPiercing;
    private int pierceCount;
    private int currentPierces;
    
    private bool isChaining;
    private int chainCount;
    private int currentChains;
    private GameObject lastTarget;
    
    private bool isFork;
    private int forkCount;
    private float forkAngle;
    private bool hasForked;
    
    private bool hasAreaDamage;
    private float areaRadius;
    
    // Store current critical hit result to share between main hit and area damage
    protected bool _currentCritResult;
    protected bool _hasRolledCrit;
     
    protected Vector2 _direction;
    protected float _timeAlive;
    protected Collider2D _collider2D;
    protected bool _isActive;
    protected int _currentLayer;
    
    // Pre-allocated arrays for zero-GC collision detection - optimized sizes prevent Unity internal resizing
    private static readonly RaycastHit2D[] castResults = new RaycastHit2D[64];
    private static readonly Collider2D[] overlapResults = new Collider2D[64];
    private ContactFilter2D contactFilter;
    private LayerMask originalLayerMask;
    
    // State variables requiring pool reset
    protected bool hasBeenHit = false;
    protected readonly System.Collections.Generic.HashSet<int> hitTargetIDs = new System.Collections.Generic.HashSet<int>();
    protected Transform cachedTarget;
    
    protected virtual void Awake()
    {
        _collider2D = GetComponent<Collider2D>();
    }
    
    private void Start()
    {
        // Initialize contact filter for collision detection
        contactFilter = new ContactFilter2D();
        contactFilter.useTriggers = false;
        // Layer mask will be set in Initialize method
    }
    
    public void Initialize(Vector2 position, Vector2 direction, float damageValue = 1f, int layer = GameLayers.PlayerProjectile)
    {
        Initialize(position, direction, damageValue, layer, defaultSpeed, defaultLifetime);
    }
    
    public void Initialize(Vector2 position, Vector2 direction, float damageValue, int layer, float projectileSpeed, float projectileLifetime)
    {
        transform.position = position;
        _direction = direction.normalized;
        _timeAlive = 0f;
        // Use the final calculated damage directly instead of multiplying by prefab's baseDamage
        this.damage = damageValue;
        this.speed = projectileSpeed;
        this.lifetime = projectileLifetime;
        _isActive = true;
        hasBeenHit = false;
        
        // Set Unity layer and configure collision filter
        if (layer < 0 || layer > 31)
        {
            Debug.LogError($"[Projectile] Invalid layer {layer}! Must be 0-31. Using PlayerProjectile layer {GameLayers.PlayerProjectile} instead.");
            layer = GameLayers.PlayerProjectile;
        }
        
        gameObject.layer = layer;
        _currentLayer = layer;
        
        // Configure contact filter based on projectile layer
        LayerMask targetMask = GetTargetLayerMask(layer);
        contactFilter.SetLayerMask(targetMask);
        originalLayerMask = targetMask;
        
        // Rotate to face direction
        float angle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
        transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        
        // Start trail particles if enabled
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StartContinuousEffect(trailParticleType, transform, trailInterval);
        }
    }
    
    /// <summary>
    /// Get target layer mask based on projectile layer
    /// </summary>
    protected LayerMask GetTargetLayerMask(int projectileLayer)
    {
        switch (projectileLayer)
        {
            case GameLayers.PlayerProjectile:
                // Player projectiles hit enemies, walls, environment
                return GameLayers.EnemyMask | GameLayers.WallMask | GameLayers.EnvironmentMask;
            case GameLayers.EnemyProjectile:
                // Enemy projectiles hit player, walls, environment
                return GameLayers.PlayerMask | GameLayers.WallMask | GameLayers.EnvironmentMask;
            default:
                // Default to player projectile behavior
                return GameLayers.EnemyMask | GameLayers.WallMask | GameLayers.EnvironmentMask;
        }
    }
    
    /// <summary>
    /// Check if projectile can damage target layer
    /// </summary>
    protected bool CanDamageLayer(int targetLayer)
    {
        switch (_currentLayer)
        {
            case GameLayers.PlayerProjectile:
                return GameLayers.IsEnemy(targetLayer);
            case GameLayers.EnemyProjectile:
                return GameLayers.IsPlayer(targetLayer);
            default:
                return false;
        }
    }
    
    protected virtual void Update()
    {
        if (!_isActive || hasBeenHit) return;
        
        // Move projectile
        Vector2 movement = _direction * speed * Time.deltaTime;
        
        // Check for collisions using CircleCast for moving objects
        CheckCollisionsDuringMovement(movement);
        
        // Apply movement after collision check
        transform.position += (Vector3)movement;
        _timeAlive += Time.deltaTime;
        
        // Check if exceeded lifetime
        if (_timeAlive >= lifetime)
        {
            SpawnDespawnParticles();
            Deactivate();
        }
    }
    
    /// <summary>
    /// Check for collisions during movement using CircleCast
    /// </summary>
    protected void CheckCollisionsDuringMovement(Vector2 movement)
    {
        if (movement.magnitude <= 0f) return;
        
        float distance = movement.magnitude;
        Vector2 direction = movement.normalized;
        
        // Use CircleCast for moving collision detection
        int hits = Physics2D.CircleCast(
            transform.position,
            collisionRadius,
            direction,
            contactFilter,
            castResults,
            distance
        );
        
        ProcessCollisionResults(hits);
    }
    
    /// <summary>
    /// Process collision results based on hit processing mode
    /// </summary>
    private void ProcessCollisionResults(int hitCount)
    {
        if (hitCount == 0) return;
        
        if (hitProcessingMode == HitProcessingMode.FirstHitOnly)
        {
            // Early exit optimization - process only first hit
            ProcessHit(castResults[0]);
            return; // Early exit - performance boost
        }
        
        // Process multiple hits (rare for projectiles)
        for (int i = 0; i < hitCount; i++)
        {
            ProcessHit(castResults[i]);
        }
    }
    
    /// <summary>
    /// Process individual collision hit
    /// </summary>
    protected virtual void ProcessHit(RaycastHit2D hit)
    {
        if (hit.collider == null) return;
        
        GameObject target = hit.collider.gameObject;
        int targetID = target.GetInstanceID();
        
        // Skip if we're chaining and this is our last target
        if (isChaining && target == lastTarget) return;
        
        // Skip if already hit this target (for piercing projectiles)
        if (hitTargetIDs.Contains(targetID)) return;
        
        // Check if target is on a layer we can damage
        int targetLayer = target.layer;
        bool canDamage = CanDamageLayer(targetLayer);
        
        if (canDamage && ApplyDamage(target))
        {
            hitTargetIDs.Add(targetID);
            cachedTarget = target.transform;
            
            // Spawn impact particles
            SpawnImpactParticles(hit.point);
            
            // Handle area damage
            if (hasAreaDamage)
            {
                ApplyAreaDamage(transform.position);
            }
            
            // Handle forking (fork takes priority over chain)
            if (isFork && !hasForked)
            {
                ForkProjectilesFromHit(hit);
                hasForked = true;
                // Deactivate the original projectile after forking
                Deactivate();
                return;
            }
            
            // Handle chaining
            if (isChaining && currentChains < chainCount)
            {
                ChainToNextTarget(target);
                currentChains++;
                return;
            }
            
            // Handle piercing
            if (isPiercing && currentPierces < pierceCount)
            {
                currentPierces++;
                return; // Continue through target
            }
            
            // Deactivate after successful hit
            hasBeenHit = true;
            Deactivate();
            return;
        }
        
        // Check for wall/environment collision
        if (GameLayers.IsWall(targetLayer) || GameLayers.IsEnvironment(targetLayer))
        {
            // Spawn impact particles for wall hits
            SpawnImpactParticles(hit.point);
            hasBeenHit = true;
            Deactivate();
        }
    }
    
    protected virtual bool ApplyDamage(GameObject target)
    {   
        // Safety check: Don't apply damage if we have 0 damage (indicates reset state)
        if (damage <= 0f)
        {
            return false;
        }

        // Calculate crit result - roll only once for the entire projectile lifetime (including chains)
        if (!_hasRolledCrit)
        {
            _currentCritResult = Random.Range(0f, 100f) < critChance;
            _hasRolledCrit = true;
        }
        bool isCrit = _currentCritResult;
        float finalDamage = damage; // Base damage for enemy fallback only

        // Create damage info with gem data for status effect configuration
        DamageInfo damageInfo;
        if (damageBreakdown.HasValue && damageBreakdown.Value.TotalDamage > 0f)
        {
            // Scale the damage breakdown with crit multiplier if needed
            var scaledBreakdown = damageBreakdown.Value;
            if (isCrit)
            {
                scaledBreakdown.ScaleAllDamage(critMultiplier);
            }
            
            // Use scaled damage breakdown (includes conversion info)
            damageInfo = DamageInfo.FromBreakdown(
                scaledBreakdown,
                isCrit,
                critMultiplier,
                "Projectile",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        else if (skillGemData != null)
        {
            // Player skill without valid breakdown - this should not happen!
            Debug.LogError($"[Projectile] Player skill '{skillGemData.gemName}' missing damage breakdown! Support gem effects like Brutality Support will be bypassed. HasValue: {damageBreakdown.HasValue}, TotalDamage: {(damageBreakdown.HasValue ? damageBreakdown.Value.TotalDamage : 0f)}");
            return false;
        }
        else
        {
            // Agent/Enemy skill fallback - use single damage type
            // Apply crit multiplier only for enemy fallback case
            if (isCrit)
            {
                finalDamage *= critMultiplier;
            }
            
            damageInfo = DamageInfo.FromSingleType(
                Mathf.RoundToInt(finalDamage),
                damageType,
                isCrit,
                critMultiplier,
                "Projectile",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        
        // First, handle player damage via PlayerManager to avoid unnecessary pool lookups
        if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
        {
            PlayerManager.DealDamageToPlayer(damageInfo);

            // Log damage for debugging
            string skillName = skillGemData?.gemName ?? "Projectile";
            DamageLogger.LogDamageInfo(skillName, damageInfo, "Projectile");

            return true;
        }

        // Check for CombatantHealth first (enemies use this) - use PoolManager for GC-free lookups
        if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
        {
            combatantHealth.TakeDamage(damageInfo);

            // Log damage for debugging
            string skillName = skillGemData?.gemName ?? "Projectile";
            DamageLogger.LogDamageInfo(skillName, damageInfo, "Projectile");

            return true;
        }
        // Fall back to HealthComponent for other targets - also use PoolManager
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
        {
            healthComponent.TakeDamage(damageInfo);
            return true;
        }

        return false;
    }
    
    private void ApplyAreaDamage(Vector3 center)
    {
        // Find all enemies in radius using modern Physics2D API
        LayerMask enemyMask = GameLayers.EnemyMask;
        
        // Create temporary ContactFilter2D for area damage detection
        ContactFilter2D areaFilter = new ContactFilter2D();
        areaFilter.SetLayerMask(enemyMask);
        areaFilter.useTriggers = false;
        
        int hits = Physics2D.OverlapCircle(
            center, 
            areaRadius, 
            areaFilter,
            overlapResults
        );
        
        for (int i = 0; i < hits; i++)
        {
            var target = overlapResults[i].gameObject;
            if (target == gameObject) continue; // Skip self
            
            // Apply reduced damage for area effect (70% of base)
            float areaDamage = damage * 0.7f;
            // Use the same critical hit result as the main projectile hit
            bool isCrit = _currentCritResult;
            if (isCrit)
            {
                areaDamage *= critMultiplier;
            }
            
            // Create damage info for area damage with gem data
            DamageInfo areaDamageInfo;
            if (damageBreakdown.HasValue)
            {
                // Scale damage breakdown for area damage
                var scaledBreakdown = damageBreakdown.Value;
                
                // Apply area damage reduction (70% of base damage)
                scaledBreakdown.ScaleAllDamage(0.7f);
                
                // Apply critical hit multiplier to the breakdown if needed
                if (isCrit)
                {
                    scaledBreakdown.ScaleAllDamage(critMultiplier);
                }

                areaDamageInfo = DamageInfo.FromBreakdown(
                    scaledBreakdown,
                    isCrit,
                    critMultiplier,
                    "Projectile_Area",
                    ailmentChance,
                    skillGemData,
                    supportGems
                );
            }
            else if (skillGemData != null)
            {
                // Player skill without breakdown - this should not happen!
                Debug.LogError($"[Projectile] Player skill '{skillGemData.gemName}' missing damage breakdown for area damage! Support gem effects like Brutality Support will be bypassed.");
                return;
            }
            else
            {
                // Agent/Enemy skill fallback - use single damage type for area damage
                areaDamageInfo = DamageInfo.FromSingleType(
                    Mathf.RoundToInt(areaDamage),
                    damageType,
                    isCrit,
                    critMultiplier,
                    "Projectile_Area",
                    ailmentChance,
                    skillGemData,
                    supportGems
                );
            }
            
            // Handle player damage via PlayerManager
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                PlayerManager.DealDamageToPlayer(areaDamageInfo);

                // Log area damage for debugging
                string skillName = skillGemData?.gemName ?? "Projectile";
                DamageLogger.LogDamageInfo(skillName, areaDamageInfo, "Area Effect");
            }
            // Handle enemy damage via PoolManager - check CombatantHealth first for GC-free lookups
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
            {
                combatantHealth.TakeDamage(areaDamageInfo);

                // Log area damage for debugging
                string skillName = skillGemData?.gemName ?? "Projectile";
                DamageLogger.LogDamageInfo(skillName, areaDamageInfo, "Area Effect");
            }
            // Fall back to HealthComponent via PoolManager
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                healthComponent.TakeDamage(areaDamageInfo);
            }
        }
    }
    
    private void ChainToNextTarget(GameObject currentTarget)
    {
        lastTarget = currentTarget;
        
        // Find nearest enemy within chain range using modern Physics2D API
        float searchRadius = 5f;
        LayerMask enemyMask = GameLayers.EnemyMask;
        
        // Create temporary ContactFilter2D for chain target detection
        ContactFilter2D chainFilter = new ContactFilter2D();
        chainFilter.SetLayerMask(enemyMask);
        chainFilter.useTriggers = false;
        
        int hits = Physics2D.OverlapCircle(
            transform.position, 
            searchRadius, 
            chainFilter,
            overlapResults
        );
        
        GameObject nearestTarget = null;
        float nearestDistance = float.MaxValue;
        
        for (int i = 0; i < hits; i++)
        {
            var target = overlapResults[i].gameObject;
            if (target == currentTarget) continue; // Skip current target
            if (target == gameObject) continue; // Skip self
            
            // Check if target has health (is damageable) - use PoolManager for GC-free lookups
            bool hasHealth = false;
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                hasHealth = true;
            }
            else if (PoolManager.Instance != null && 
                    (PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var _) ||
                     PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var _)))
            {
                hasHealth = true;
            }
            
            if (hasHealth)
            {
                float distance = Vector2.Distance(transform.position, target.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestTarget = target;
                }
            }
        }
        
        if (nearestTarget != null)
        {
            // Redirect projectile to new target
            Vector2 newDirection = (nearestTarget.transform.position - transform.position).normalized;
            _direction = newDirection;
            
            // Reset lifetime for chain
            _timeAlive = 0f;
            
            // Reduce damage for each chain (80% of previous)
            damage *= 0.8f;
            
            // Scale damage breakdown to match reduced damage
            if (damageBreakdown.HasValue)
            {
                var scaledBreakdown = damageBreakdown.Value;
                scaledBreakdown.ScaleAllDamage(0.8f);
                damageBreakdown = scaledBreakdown;
            }
            
            // Update rotation
            float angle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
            transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        }
        else
        {
            // No valid targets, deactivate
            Deactivate();
        }
    }
    
    private void ForkProjectilesFromHit(RaycastHit2D hit)
    {
        if (PoolManager.Instance == null || hit.collider == null) return;
        
        Vector2 impactPoint = hit.point;
        Vector2 collisionNormal = hit.normal;
        
        // Calculate offset based on the hit object's collider bounds
        float spawnOffset = 0.2f; // Base offset
        
        if (hit.collider is CircleCollider2D circleCol)
        {
            // For circles, use radius
            spawnOffset += circleCol.radius;
        }
        else if (hit.collider is BoxCollider2D boxCol)
        {
            // For boxes, use the larger dimension
            spawnOffset += Mathf.Max(boxCol.size.x, boxCol.size.y) * 0.5f;
        }
        
        // Add a small safety margin
        spawnOffset += 0.3f;
        
        // Spawn point: offset from impact point along the collision normal
        // The normal points away from the collider we hit
        Vector2 spawnOrigin = impactPoint + (collisionNormal * spawnOffset);
        
        // Debug.Log($"Fork: impact={impactPoint}, normal={collisionNormal}, offset={spawnOffset}, spawn={spawnOrigin}");
        
        // Calculate the base angle spread
        float anglePerFork = forkCount > 1 ? forkAngle / (forkCount - 1) : 0f;
        float startAngle = -forkAngle / 2f;
        
        // Get the current projectile's direction angle
        float baseAngle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
        
        // Spawn forked projectiles
        for (int i = 0; i < forkCount; i++)
        {
            float currentAngle = baseAngle + startAngle + (anglePerFork * i);
            float radians = currentAngle * Mathf.Deg2Rad;
            Vector2 forkDirection = new Vector2(Mathf.Cos(radians), Mathf.Sin(radians));
            
            // Retrieve the original prefab for the current projectile
            GameObject projectilePrefab = PoolManager.Instance.GetOriginalPrefab(gameObject) ?? gameObject;
            
            // Spawn a new projectile from the original prefab (avoids creating a pool for 'Bullet(Clone)')
            GameObject forkedProjectile = PoolManager.Instance.Spawn(projectilePrefab, spawnOrigin, Quaternion.Euler(0, 0, currentAngle));
            if (forkedProjectile != null && PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<Projectile>(forkedProjectile, out var projectileComponent))
            {
                // Initialize the forked projectile first (sets damage, speed, position, etc.)
                float forkedDamage = damage * 0.7f;
                projectileComponent.Initialize(spawnOrigin, forkDirection, forkedDamage, _currentLayer, speed, lifetime);

                // CRITICAL: Set status effect properties AFTER Initialize to ensure they persist
                // Initialize() doesn't override these properties, but we set them after to be safe
                projectileComponent.damageType = damageType;
                projectileComponent.ailmentChance = ailmentChance;
                projectileComponent.skillGemData = skillGemData;
                projectileComponent.supportGems = supportGems;
                projectileComponent.critChance = critChance;
                projectileComponent.critMultiplier = critMultiplier;
                
                // CRITICAL: Transfer damage breakdown to preserve support gem effects (e.g., Brutality Support)
                if (damageBreakdown.HasValue)
                {
                    var scaledBreakdown = damageBreakdown.Value;
                    // Scale the breakdown to match the forked damage (70% of original)
                    scaledBreakdown.ScaleAllDamage(0.7f);
                    projectileComponent.damageBreakdown = scaledBreakdown;
                }

                // Forked projectiles can pierce but not fork again
                if (isPiercing)
                {
                    projectileComponent.SetPiercing(true, pierceCount);
                }

                // Disable forking on the forked projectiles to prevent infinite forks
                projectileComponent.SetFork(false);
            }
        }
    }
    
    protected virtual void SpawnImpactParticles(Vector2 impactPoint)
    {
        if (!useImpactParticles || ParticleEffectManager.Instance == null) return;
        
        // Use particle spawn point if available, otherwise use impact point
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : (Vector3)impactPoint;
        
        ParticleEffectManager.Instance.SpawnParticle(impactParticleType, spawnPosition, impactParticleCount);
    }
    
    protected virtual void SpawnDespawnParticles()
    {
        if (!useDespawnParticles || ParticleEffectManager.Instance == null) return;
        
        // Use particle spawn point if available, otherwise use current position
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : transform.position;
        
        ParticleEffectManager.Instance.SpawnParticle(despawnParticleType, spawnPosition, despawnParticleCount);
    }
    
    protected virtual void Deactivate()
    {
        _isActive = false;
        
        // Stop trail particles if active
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
        
        if (PoolManager.Instance != null)
        {
            PoolManager.Instance.Despawn(gameObject);
        }
        else
        {
            gameObject.SetActive(false);
        }
    }
    
    // ISpawnable implementation
    public virtual void OnSpawn()
    {
        _isActive = true;
        hasBeenHit = false;
    }
    
    public virtual void OnDespawn()
    {
        // CRITICAL: Complete state reset for pool reuse
        _isActive = false;
        hasBeenHit = false;
        damage = 0f; // Reset damage (will be set by Initialize)
        speed = defaultSpeed; // Reset speed
        lifetime = defaultLifetime; // Reset lifetime
        damageType = DamageType.Physical; // Reset damage type
        ailmentChance = 0f; // Reset ailment chance
        damageBreakdown = null; // Reset damage breakdown

        // Clear gem data references
        skillGemData = null;
        supportGems = null;
        
        // Reset support gem effects
        isPiercing = false;
        pierceCount = 0;
        currentPierces = 0;
        
        isChaining = false;
        chainCount = 0;
        currentChains = 0;
        lastTarget = null;
        
        isFork = false;
        forkCount = 0;
        forkAngle = 30f;
        hasForked = false;
        
        hasAreaDamage = false;
        areaRadius = 0f;
        
        // Reset critical hit tracking
        _currentCritResult = false;
        _hasRolledCrit = false;
        
        // Clear collision tracking collections
        hitTargetIDs.Clear();
        
        // Clear pre-allocated arrays using Array.Clear for performance
        System.Array.Clear(castResults, 0, castResults.Length);
        System.Array.Clear(overlapResults, 0, overlapResults.Length);
        
        // Reset ContactFilter2D if modified at runtime
        if (contactFilter.layerMask != originalLayerMask)
        {
            contactFilter.SetLayerMask(originalLayerMask);
        }
        
        // Clear object references - prevents memory leaks
        cachedTarget = null;
        
        // Ensure particles are stopped
        if (ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
    }
    
    // Support gem effect setters
    public void SetPiercing(bool enable, int count = 999)
    {
        isPiercing = enable;
        pierceCount = count;
        currentPierces = 0;
    }
    
    public void SetChaining(bool enable, int count = 3)
    {
        isChaining = enable;
        chainCount = count;
        currentChains = 0;
    }
    
    public void SetFork(bool enable, int count = 2, float angle = 30f)
    {
        isFork = enable;
        forkCount = count;
        forkAngle = angle;
        hasForked = false;
    }
    
    public void SetAreaDamage(bool enable, float radius = 2f)
    {
        hasAreaDamage = enable;
        areaRadius = radius;
    }

    // Debug visualization for collision radius
    void OnDrawGizmosSelected()
    {
        if (!showCollisionDebug) return;
        
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, collisionRadius);
        
        // Hit processing mode indicator
        switch (hitProcessingMode)
        {
            case HitProcessingMode.FirstHitOnly:
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(transform.position + Vector3.up * (collisionRadius + 0.5f), Vector3.one * 0.3f);
                break;
                
            case HitProcessingMode.AllHits:
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(transform.position + Vector3.up * (collisionRadius + 0.5f), 0.2f);
                break;
                
            case HitProcessingMode.MaxHits:
                Gizmos.color = Color.blue;
                // Draw indicator for max hits mode
                Gizmos.DrawWireCube(transform.position + Vector3.up * (collisionRadius + 0.5f), Vector3.one * 0.2f);
                break;
        }
    }
}