using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using Sirenix.OdinInspector;

/// <summary>
/// UI component that displays an unlocked item icon in the Game Over screen.
/// Simple display-only slot that shows what the player has unlocked.
/// </summary>
public class UnlockSlot : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    [Title("Icon Display")]
    [SerializeField, Required] private LayeredIconDisplay layeredIconDisplay;
    
    [Title("Optional Border")]
    [SerializeField] private Image borderImage;
    [SerializeField] private Color unlockedBorderColor = Color.green;
    
    [Title("Optional Background")]
    [SerializeField] private Image backgroundImage;
    [SerializeField] private Color unlockedBackgroundColor = new Color(0f, 1f, 0f, 0.2f);
    
    [Title("Tooltip (Optional)")]
    [SerializeField] private bool enableTooltip = true;
    
    [Title("Debug")]
    [ReadOnly, ShowInInspector] private GemData currentUnlockedGem;
    [ReadOnly, ShowInInspector] private bool isSetup = false;
    
    private void Awake()
    {
        InitializeSlot();
    }
    
    private void InitializeSlot()
    {
        // Initialize border with unlocked style
        if (borderImage != null)
        {
            borderImage.color = unlockedBorderColor;
        }
        
        // Initialize background with unlocked style
        if (backgroundImage != null)
        {
            backgroundImage.color = unlockedBackgroundColor;
        }
        
        // Ensure layered icon display is ready
        if (layeredIconDisplay == null)
        {
            layeredIconDisplay = GetComponentInChildren<LayeredIconDisplay>();
            if (layeredIconDisplay == null)
            {
                Debug.LogError("UnlockSlot: LayeredIconDisplay component not found! Please assign it in the inspector.", this);
            }
        }
        
        isSetup = true;
    }
    
    /// <summary>
    /// Sets the unlocked gem to display in this slot
    /// </summary>
    public void SetUnlockedGem(GemData gemData)
    {
        if (!isSetup)
        {
            InitializeSlot();
        }
        
        currentUnlockedGem = gemData;
        
        if (layeredIconDisplay != null && gemData != null)
        {
            // Check if gem has layered icons
            if (gemData.HasLayeredIcons())
            {
                layeredIconDisplay.SetManualLayeredIcon(
                    gemData.GetBackgroundIcon(), 
                    gemData.GetForegroundIcon()
                );
            }
            else
            {
                // Use single icon mode
                layeredIconDisplay.SetSingleIcon(gemData.GetIcon());
            }
            
            // Apply rarity color to border if available
            if (borderImage != null)
            {
                Color rarityColor = gemData.GetRarityColor();
                borderImage.color = Color.Lerp(unlockedBorderColor, rarityColor, 0.7f);
            }
        }
        else if (layeredIconDisplay != null)
        {
            // Clear icon if no gem data
            layeredIconDisplay.ClearIcon();
        }
    }
    
    /// <summary>
    /// Clears the slot
    /// </summary>
    public void ClearSlot()
    {
        currentUnlockedGem = null;
        
        if (layeredIconDisplay != null)
        {
            layeredIconDisplay.ClearIcon();
        }
        
        // Reset border to default unlocked color
        if (borderImage != null)
        {
            borderImage.color = unlockedBorderColor;
        }
    }
    
    /// <summary>
    /// Gets the currently displayed gem
    /// </summary>
    public GemData GetUnlockedGem()
    {
        return currentUnlockedGem;
    }
    
    /// <summary>
    /// Checks if this slot has a gem assigned
    /// </summary>
    public bool HasGem()
    {
        return currentUnlockedGem != null;
    }
    
    // Tooltip support
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (enableTooltip && currentUnlockedGem != null)
        {
            ShowTooltip();
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        if (enableTooltip)
        {
            HideTooltip();
        }
    }
    
    private void ShowTooltip()
    {
        // Simple tooltip implementation - can be extended with proper tooltip system
        if (currentUnlockedGem != null)
        {
            string tooltipText = $"<b>{currentUnlockedGem.gemName}</b>\n{currentUnlockedGem.description}";
            
            // Try to find tooltip system - if none exists, just debug log
            var tooltipSystem = FindObjectOfType<Canvas>()?.GetComponentInChildren<TMPro.TMP_Text>();
            if (tooltipSystem != null)
            {
                // Basic tooltip display - this could be enhanced with proper tooltip system
                Debug.Log($"Tooltip: {tooltipText}");
            }
        }
    }
    
    private void HideTooltip()
    {
        // Hide tooltip if tooltip system exists
        // For now just a placeholder
    }
    
    #if UNITY_EDITOR
    [Title("Debug Tools")]
    [FoldoutGroup("Debug")]
    [Button("Test with Random Gem", ButtonSizes.Medium)]
    private void TestWithRandomGem()
    {
        if (!Application.isPlaying) return;
        
        // Find a test gem from resources
        var skillGems = Resources.FindObjectsOfTypeAll<SkillGemData>();
        if (skillGems.Length > 0)
        {
            var randomGem = skillGems[Random.Range(0, skillGems.Length)];
            SetUnlockedGem(randomGem);
            Debug.Log($"Testing UnlockSlot with gem: {randomGem.gemName}");
        }
        else
        {
            Debug.LogWarning("No SkillGemData found for testing!");
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Clear Test Gem", ButtonSizes.Small)]
    private void ClearTestGem()
    {
        if (Application.isPlaying)
        {
            ClearSlot();
            Debug.Log("UnlockSlot cleared.");
        }
    }
    
    [FoldoutGroup("Debug")]
    [ShowInInspector]
    [PropertySpace]
    private string SlotStatus => 
        $"Setup: {(isSetup ? "✓" : "✗")}\n" +
        $"Has Gem: {(HasGem() ? "✓" : "✗")}\n" +
        $"Current Gem: {(currentUnlockedGem != null ? currentUnlockedGem.gemName : "None")}\n" +
        $"Icon Display: {(layeredIconDisplay != null ? "✓" : "✗")}\n" +
        $"Border: {(borderImage != null ? "✓" : "✗")}\n" +
        $"Background: {(backgroundImage != null ? "✓" : "✗")}";
    #endif
}