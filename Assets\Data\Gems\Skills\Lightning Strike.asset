%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc7afdaae02bac047acf72aabe6a2a9c, type: 3}
  m_Name: Lightning Strike
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SkillGemData
  icon: {fileID: -266756712594997613, guid: 4278a010334d7834298fffba4e477be0, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Lightning Strike
  description: Calls down powerful bolts of lightning from the sky, striking enemies
    at the targeted location.
  rarity: 1
  requiresUnlock: 1
  unlockCondition: {fileID: 11400000, guid: ac6a34241f35f3b4bb0321f0f59b9cfd, type: 2}
  skillType: 0
  gemTags: 44
  skillPrefab: {fileID: 2340940985034927489, guid: 11f6587f6882ab04387d04c2df3afc2f, type: 3}
  baseDamage: 12
  cooldown: 2.5
  manaCost: 8
  projectileSpeed: 10
  duration: 2
  waveAmplitude: 0.08
  waveFrequency: 8
  serpentineDelay: 0.2
  projectileCount: 1
  projectileDelay: 0.1
  orbitRadius: 2
  rotationSpeed: 180
  bladeCount: 3
  targetGroundPosition: 1
  attackSpeedMultiplier: 1
  projectileLayer: 8
  critChance: 15
  critMultiplier: 2
  damageType: 3
  ailmentChance: 30
  intrinsicProjectileCount: 1
  intrinsicSpreadAngle: 20
  intrinsicUseRandomSpread: 0
  intrinsicHasPierce: 0
  intrinsicHasChain: 0
  intrinsicChainCount: 2
  projectileSpeedVariation: 1
  serpentineAmplitudeVariation: 0.2
  serpentineFrequencyVariation: 0.1
  ignitePercent: 0.2
  igniteDuration: 4
  igniteTickInterval: 0.5
  freezeSlowAmount: 0.5
  freezeDuration: 2
  bleedPercent: 0.15
  bleedDuration: 6
  bleedTickInterval: 1
  shockChainDamage: 0.15
  shockChainRange: 4
  shockDuration: 3
