using UnityEngine;

/// <summary>
/// Strategy implementation for instant/area-of-effect skills.
/// Handles ground-targeted and player-centered instant skills with multi-cast support.
/// Preserves autonomous targeting behavior for skills like Lightning Strike.
/// </summary>
public class InstantSkillExecutor : ISkillExecutor
{
    public bool CanExecute(SkillType skillType) => skillType == SkillType.Instant;

    public void Execute(SkillExecutor skillExecutor, int slotIndex, GemSocketController controller,
                       SkillGemData skillData, Vector3 targetPosition, bool isAutonomous)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        skillExecutor.CacheSkillExecutionValues(controller, slotIndex);

        // For autonomous casts we always want to use the provided target position even if
        // the skill normally spawns at the player. This fixes Lightning Strike targeting.
        // Use isAutonomous parameter instead of controller.HasAutonomous() for strategy pattern
        bool useTargetPosition = skillData.targetGroundPosition || isAutonomous;
        Vector3 baseSpawnPosition = useTargetPosition ? targetPosition : skillExecutor.transform.position;
        
        // Get spell count from support gems (multi shot)
        int spellCount = controller.GetTotalProjectileCount();
        
        // Calculate spread radius for multiple instant spells
        float spreadRadius = 0f;
        if (spellCount > 1)
        {
            // Spread radius increases with spell count (1.5 units per extra spell)
            spreadRadius = (spellCount - 1) * 1.5f;
        }
        
        // Access execution cache and cached vectors
        var executionCache = skillExecutor.GetExecutionCache(slotIndex);
        var cachedOffsetVector = skillExecutor._cachedOffsetVector;
        var cachedVector3Temp = skillExecutor._cachedVector3Temp;
        
        // Spawn multiple instant spells
        for (int i = 0; i < spellCount; i++)
        {
            Vector3 spawnPosition = baseSpawnPosition;
            
            // For multiple spells, spread them in a circle around the target
            if (spellCount > 1)
            {
                float angle = (360f / spellCount) * i * Mathf.Deg2Rad;
                cachedOffsetVector.Set(Mathf.Cos(angle), Mathf.Sin(angle));
                cachedOffsetVector *= spreadRadius;
                spawnPosition += (Vector3)cachedOffsetVector;
            }
            
            GameObject skillObject = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, Quaternion.identity);
            if (skillObject == null) continue;
            
            // Check if this is an InstantSpell component
            if (PoolManager.Instance.GetCachedComponent<InstantSpell>(skillObject, out var instantSpell))
            {
                int layer = skillData.projectileLayer; // Use gem's collision layer

                // Use cached crit stats
                instantSpell.critChance = executionCache.finalCritChance;
                instantSpell.critMultiplier = executionCache.finalCritMultiplier;
                instantSpell.damageType = executionCache.damageBreakdown.GetPredominantType(); // Use converted type!
                instantSpell.ailmentChance = skillData.ailmentChance;
                instantSpell.damageBreakdown = executionCache.damageBreakdown; // Pass full breakdown!

                // Pass gem data for status effect configuration
                instantSpell.skillGemData = skillData;
                instantSpell.supportGems = skillExecutor.GetCachedSupportGems(controller, slotIndex);

                // Pass actual damage value, not multiplier
                instantSpell.Initialize((Vector2)spawnPosition, executionCache.playerModifiedDamage, layer);
            }
            else
            {
                // Fallback for other instant skill types
                skillExecutor.ApplySkillEffects(skillObject, controller, slotIndex);
                
                // Handle area damage if supported using cached values
                if (executionCache.hasAreaDamage)
                {
                    skillExecutor.ApplyAreaDamage(spawnPosition, executionCache.playerModifiedDamage, executionCache.areaRadius, slotIndex);
                }
            }
        }
    }
}