using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Pooled AOE explosion with immediate damage calculation and visual animation.
/// Spawns, applies damage instantly, plays animation, then despawns automatically.
/// </summary>
[RequireComponent(typeof(SpriteAnimator))]
public class AOEExplosion : MonoBehaviour, ISpawnable
{
    [Title("Visual Scaling")]
    [SerializeField, Range(0.5f, 10f), Tooltip("Base radius for 1:1 sprite scale (prefab sprite designed for this radius)")]
    private float baseScaleRadius = 3f;
    
    [Title("Debug")]
    [SerializeField, Tooltip("Show AOE explosion debug info")]
    private bool showDebug = false;
    
    // Pre-allocated arrays for zero-GC collision detection
    private static readonly Collider2D[] aoeResults = new Collider2D[64];
    
    // Required components
    private SpriteRenderer spriteRenderer;
    private SpriteAnimator spriteAnimator;
    
    // AOE damage parameters (set by AOEProjectile)
    private Vector3 explosionCenter;
    private float explosionRadius;
    private float damageMultiplier;
    private LayerMask targetMask;
    private DamageBreakdown? damageBreakdown; // Nullable struct
    private SkillGemData skillGemData;
    private System.Collections.Generic.List<GemInstance> supportGems;
    private bool isCritical;
    private float critMultiplier;
    private float ailmentChance;
    private float baseDamage;
    private DamageType damageType;
    
    // Animation state
    private bool hasAppliedDamage = false;
    private bool animationCompleted = false;
    
    private void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        spriteAnimator = GetComponent<SpriteAnimator>();
        
        // Subscribe to animation events
        if (spriteAnimator != null)
        {
            spriteAnimator.OnAnimationCompleted += OnAnimationCompleted;
        }
    }
    
    private void OnDestroy()
    {
        // Unsubscribe from animation events
        if (spriteAnimator != null)
        {
            spriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
        }
    }
    
    /// <summary>
    /// Configure explosion parameters before spawning
    /// </summary>
    public void SetupExplosion(Vector3 center, float radius, float aoeDamageMultiplier, LayerMask targets,
        DamageBreakdown? breakdown = null, SkillGemData skillGem = null, 
        System.Collections.Generic.List<GemInstance> supportGemList = null,
        bool critical = false, float critMult = 1.5f, float ailment = 0f,
        float damage = 0f, DamageType type = DamageType.Physical)
    {
        explosionCenter = center;
        explosionRadius = radius;
        damageMultiplier = aoeDamageMultiplier;
        targetMask = targets;
        damageBreakdown = breakdown;
        skillGemData = skillGem;
        supportGems = supportGemList;
        isCritical = critical;
        critMultiplier = critMult;
        ailmentChance = ailment;
        baseDamage = damage;
        damageType = type;
    }
    
    // ISpawnable implementation
    public void OnSpawn()
    {
        hasAppliedDamage = false;
        animationCompleted = false;
        
        // Position at explosion center
        transform.position = explosionCenter;
        
        // Scale visual effect based on explosion radius
        float scaleMultiplier = explosionRadius / baseScaleRadius;
        
        // Ensure minimum scale to prevent invisible explosions
        scaleMultiplier = Mathf.Max(scaleMultiplier, 0.5f);
        
        transform.localScale = Vector3.one * scaleMultiplier;
        
        if (showDebug)
        {
            Debug.Log($"[AOEExplosion] Scale calculation: radius={explosionRadius}, baseRadius={baseScaleRadius}, multiplier={scaleMultiplier}");
        }
        
        // IMMEDIATELY apply damage when spawned (don't wait for animation)
        ApplyAOEDamage();
        
        // Start explosion animation
        if (spriteAnimator != null)
        {
            spriteAnimator.Play("explosion"); // Animation name can be configured
        }
        else
        {
            Debug.LogError("[AOEExplosion] No SpriteAnimator component found! Explosion will not animate or despawn properly. Please add SpriteAnimator to the prefab.");
        }
        
        if (showDebug)
        {
            Debug.Log($"[AOEExplosion] Spawned at {explosionCenter} with radius {explosionRadius}");
        }
    }
    
    public void OnDespawn()
    {
        // Reset state for pool reuse
        hasAppliedDamage = false;
        animationCompleted = false;
        explosionCenter = Vector3.zero;
        explosionRadius = 0f;
        damageMultiplier = 0f;
        damageBreakdown = null; // Reset nullable struct
        skillGemData = null;
        supportGems = null;
        
        // Reset scale for pool reuse
        transform.localScale = Vector3.one;
        
        // Clear pre-allocated arrays
        System.Array.Clear(aoeResults, 0, aoeResults.Length);
        
        // Cancel any pending invokes
        CancelInvoke();
        
        if (showDebug)
        {
            Debug.Log("[AOEExplosion] Despawned and reset for pool reuse");
        }
    }
    
    /// <summary>
    /// Apply AOE damage immediately when explosion spawns
    /// </summary>
    private void ApplyAOEDamage()
    {
        if (hasAppliedDamage) return; // Prevent double-damage
        
        hasAppliedDamage = true;
        
        if (showDebug)
        {
            Debug.Log($"[AOEExplosion] Applying AOE damage at {explosionCenter} with radius {explosionRadius}");
        }
        
        // Find all targets in explosion radius
        int hits = Physics2D.OverlapCircleNonAlloc(explosionCenter, explosionRadius, aoeResults, targetMask);
        
        if (showDebug)
        {
            Debug.Log($"[AOEExplosion] Found {hits} targets in radius");
        }
        
        // Apply damage to all targets
        for (int i = 0; i < hits; i++)
        {
            var target = aoeResults[i].gameObject;
            if (target == gameObject) continue; // Skip self
            
            ApplyDamageToTarget(target);
        }
    }
    
    /// <summary>
    /// Apply damage to a specific target
    /// </summary>
    private void ApplyDamageToTarget(GameObject target)
    {
        DamageInfo aoeDamageInfo;
        
        // Create damage info based on available data
        if (damageBreakdown.HasValue && damageBreakdown.Value.TotalDamage > 0f)
        {
            // Use DamageBreakdown for player skills
            var scaledBreakdown = new DamageBreakdown(damageBreakdown.Value);
            scaledBreakdown.ScaleAllDamage(damageMultiplier);
            
            if (isCritical)
            {
                scaledBreakdown.ScaleAllDamage(critMultiplier);
            }
            
            aoeDamageInfo = DamageInfo.FromBreakdown(
                scaledBreakdown,
                isCritical,
                critMultiplier,
                "AOE_Explosion",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        else if (skillGemData != null)
        {
            // Player skill without breakdown - error case
            Debug.LogError($"[AOEExplosion] Player skill '{skillGemData.gemName}' missing damage breakdown!");
            return;
        }
        else
        {
            // Enemy/Agent skill fallback
            float finalDamage = baseDamage * damageMultiplier;
            
            if (isCritical)
            {
                finalDamage *= critMultiplier;
            }
            
            aoeDamageInfo = DamageInfo.FromSingleType(
                Mathf.RoundToInt(finalDamage),
                damageType,
                isCritical,
                critMultiplier,
                "AOE_Explosion",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        
        // Apply damage to target
        bool damageApplied = false;
        
        // Handle player damage
        if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
        {
            PlayerManager.DealDamageToPlayer(aoeDamageInfo);
            damageApplied = true;
        }
        // Handle enemy damage via PoolManager
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
        {
            combatantHealth.TakeDamage(aoeDamageInfo);
            damageApplied = true;
        }
        // Fallback to HealthComponent
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
        {
            healthComponent.TakeDamage(aoeDamageInfo);
            damageApplied = true;
        }
        
        // Debug logging
        if (damageApplied && showDebug)
        {
            Debug.Log($"[AOEExplosion] AOE hit {target.name} for {aoeDamageInfo.amount} damage");
        }
        
        // Log damage for debugging
        if (damageApplied)
        {
            string skillName = skillGemData?.gemName ?? "AOEExplosion";
            DamageLogger.LogDamageInfo(skillName, aoeDamageInfo, "AOE Effect");
        }
    }
    
    /// <summary>
    /// Called when animation completes - automatically despawn
    /// </summary>
    private void OnAnimationCompleted(string animationName)
    {
        if (animationCompleted) return; // Prevent double-despawn
        
        animationCompleted = true;
        
        if (showDebug)
        {
            Debug.Log("[AOEExplosion] Animation completed, despawning");
        }
        
        // Despawn via PoolManager
        if (PoolManager.Instance != null)
        {
            PoolManager.Instance.Despawn(gameObject);
        }
        else
        {
            gameObject.SetActive(false);
        }
    }
    
    
    /// <summary>
    /// Debug visualization
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        if (!showDebug) return;
        
        Vector3 center = explosionCenter != Vector3.zero ? explosionCenter : transform.position;
        float radius = explosionRadius > 0 ? explosionRadius : baseScaleRadius;
        
        // Draw actual damage radius (red wire sphere)
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(center, radius);
        
        // Draw sprite visual bounds for scale comparison (green wire sphere)
        float visualRadius = radius * transform.localScale.x; // Scale factor applied
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(center, visualRadius);
        
        // Draw center point
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(center, 0.2f);
        
        // Draw scale indicator lines
        Gizmos.color = Color.cyan;
        Gizmos.DrawLine(center + Vector3.right * radius, center + Vector3.right * visualRadius);
        Gizmos.DrawLine(center + Vector3.up * radius, center + Vector3.up * visualRadius);
    }
    
    /// <summary>
    /// Always visible debug gizmos (faded)
    /// </summary>
    private void OnDrawGizmos()
    {
        if (!showDebug) return;
        
        Vector3 center = explosionCenter != Vector3.zero ? explosionCenter : transform.position;
        float radius = explosionRadius > 0 ? explosionRadius : baseScaleRadius;
        
        // Faded damage radius visualization
        Gizmos.color = new Color(1f, 0f, 0f, 0.1f);
        Gizmos.DrawSphere(center, radius);
        
        // Faded visual scale bounds
        float visualRadius = radius * transform.localScale.x;
        Gizmos.color = new Color(0f, 1f, 0f, 0.05f);
        Gizmos.DrawSphere(center, visualRadius);
    }
}