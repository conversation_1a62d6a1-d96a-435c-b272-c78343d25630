using System.Collections.Generic;
using UnityEngine;

public class ObjectPool
{
    private readonly GameObject _prefab;
    /// <summary>Reference to the prefab used by this pool.</summary>
    public GameObject Prefab => _prefab;
    private readonly Queue<GameObject> _availableObjects = new Queue<GameObject>();
    private readonly Transform _parentTransform;
    private readonly List<ISpawnable> _spawnableBuffer = new List<ISpawnable>();
    private readonly Dictionary<GameObject, Dictionary<System.Type, Component>> _componentCache = new Dictionary<GameObject, Dictionary<System.Type, Component>>();

    // Runtime statistics
    private int _totalCreated;
    private int _activeCount;
    private int _peakActiveCount;
    private float _activeCountSum;
    private int _activeSampleCount;

    /// <summary>
    /// Total number of objects that have been created for this pool (active + inactive).
    /// </summary>
    public int TotalCount => _totalCreated;

    /// <summary>
    /// Number of currently active (spawned) objects.
    /// </summary>
    public int ActiveCount => _activeCount;

    /// <summary>
    /// Number of currently inactive (despawned) objects in the pool.
    /// </summary>
    public int InactiveCount => _totalCreated - _activeCount;

    /// <summary>
    /// Peak number of active objects that have been spawned simultaneously.
    /// </summary>
    public int PeakActiveCount => _peakActiveCount;

    /// <summary>
    /// Average number of active objects over time.
    /// </summary>
    public float AverageActiveCount => _activeSampleCount > 0 ? _activeCountSum / _activeSampleCount : 0f;

    public int GrowSize { get; set; } = 5;

    public ObjectPool(GameObject prefab, int initialSize, Transform parent)
    {
        _prefab = prefab;
        _parentTransform = parent;
        ExpandPool(initialSize);
    }

    public GameObject Spawn(Vector3 position, Quaternion rotation, Transform parent)
    {
        // Clean up any destroyed objects from the queue
        while (_availableObjects.Count > 0 && _availableObjects.Peek() == null)
        {
            _availableObjects.Dequeue();
        }
        
        if (_availableObjects.Count == 0)
        {
            ExpandPool(GrowSize);
        }

        GameObject obj = _availableObjects.Dequeue();
        
        // Double-check the object is valid
        if (obj == null)
        {
            Debug.LogError($"ObjectPool: Spawned object was null! Pool for prefab: {_prefab.name}");
            ExpandPool(1);
            obj = _availableObjects.Dequeue();
        }
        
        _activeCount++; // track active
        
        // Update peak tracking
        if (_activeCount > _peakActiveCount)
        {
            _peakActiveCount = _activeCount;
        }
        
        // Update average tracking
        _activeCountSum += _activeCount;
        _activeSampleCount++;

        obj.transform.SetPositionAndRotation(position, rotation);
        obj.transform.SetParent(parent, true);
        obj.transform.localScale = _prefab.transform.localScale;
        obj.SetActive(true);

        obj.GetComponentsInChildren(true, _spawnableBuffer);
        foreach (var spawnable in _spawnableBuffer)
        {
            spawnable.OnSpawn();
        }

        return obj;
    }

    public void Despawn(GameObject obj)
    {
        if (obj == null)
        {
            Debug.LogWarning("ObjectPool: Attempted to despawn null object");
            return;
        }
        
        obj.GetComponentsInChildren(true, _spawnableBuffer);
        foreach (var spawnable in _spawnableBuffer)
        {
            spawnable.OnDespawn();
        }
        
        _activeCount = Mathf.Max(0, _activeCount - 1);
        
        // Update average tracking
        _activeCountSum += _activeCount;
        _activeSampleCount++;
        obj.SetActive(false);
        obj.transform.SetParent(_parentTransform);
        _availableObjects.Enqueue(obj);
    }

    public bool GetCachedComponent<T>(GameObject obj, out T component) where T : Component
    {
        component = null;
        if (!_componentCache.ContainsKey(obj))
        {
            _componentCache[obj] = new Dictionary<System.Type, Component>();
        }

        if (_componentCache[obj].TryGetValue(typeof(T), out Component cachedComponent))
        {
            component = cachedComponent as T;
            return component != null;
        }
        
        T newComponent = obj.GetComponent<T>();
        if (newComponent != null)
        {
            _componentCache[obj][typeof(T)] = newComponent;
            component = newComponent;
            return true;
        }
        return false;
    }

    private void ExpandPool(int amount)
    {
        for (int i = 0; i < amount; i++)
        {
            GameObject obj = Object.Instantiate(_prefab, _parentTransform);
            obj.SetActive(false);
            _availableObjects.Enqueue(obj);
            _componentCache[obj] = new Dictionary<System.Type, Component>();

            _totalCreated++;
        }
    }

    /// <summary>
    /// Resets the peak and average statistics. Useful for profiling specific scenarios.
    /// </summary>
    public void ResetStatistics()
    {
        _peakActiveCount = _activeCount; // Reset to current active count
        _activeCountSum = 0f;
        _activeSampleCount = 0;
    }
}