%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &7671528889692876913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2299645079242175685}
  - component: {fileID: 2768126783768394208}
  - component: {fileID: -8365770041160165828}
  - component: {fileID: -4348043867358150448}
  - component: {fileID: -615261834649403541}
  - component: {fileID: 8973770144313412763}
  - component: {fileID: 6594840336060423115}
  m_Layer: 16
  m_Name: BreachPurple
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2299645079242175685
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7671528889692876913}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 18.62, y: 5.06, z: 0}
  m_LocalScale: {x: 25, y: 25, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &2768126783768394208
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7671528889692876913}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c8d8f4fc1d8d4f24fa85cfae8f001e84, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: -2413806693520163455, guid: a86470a33a6bf42c4b3595704624658b, type: 3}
  m_Color: {r: 0.5783034, g: 0.44014773, b: 0.5943396, a: 0.6745098}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!114 &-8365770041160165828
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7671528889692876913}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4627387aa9aea8445b332ad98003c21e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::BreachEnemySpawner
  settings: {fileID: 11400000, guid: 02e83ee0c8e08a449bd0f7bd917a0259, type: 2}
  enableSpawning: 1
  useCustomValues: 1
  enemyRaceData: {fileID: 11400000, guid: 90ae6a73c650a6844ae6b2c2de3dd384, type: 2}
  customSpawnInterval: 1.5
  customMinEnemiesPerSpawn: 4
  customMaxEnemiesPerSpawn: 8
  customMaxSpawnedEnemies: 30
  customMinSpawnRadiusFactor: 0.3
  customMaxSpawnRadiusFactor: 0.8
  customSpawnDelay: 0.5
  customSpawnParticleType: 8
  customParticleToEnemyDelay: 0.3
  customBreachEnemyType: 7
  customAllowedSplinterTypes: 32
  enableSplinterDrops: 1
  breachSplinterConfig: {fileID: 11400000, guid: 21260a901121892478544c3b2ec97a11, type: 2}
  enableDebugLogging: 0
--- !u!114 &-4348043867358150448
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7671528889692876913}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9a725f6f7c9931640bb7fa586faf6fbf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::BreachRadiusAnimator
  settings: {fileID: 11400000, guid: 02e83ee0c8e08a449bd0f7bd917a0259, type: 2}
  enableAnimation: 1
  useCustomValues: 1
  customStartRadius: 1.3
  customEndRadius: 12
  customDuration: 6
  customEase: 27
  customDelay: 0
  resetOnDisable: 1
  enableDebugLogging: 0
--- !u!114 &-615261834649403541
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7671528889692876913}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 504c14106234d224fadce2038847a623, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::BreachMaterialAnimator
  settings: {fileID: 11400000, guid: 02e83ee0c8e08a449bd0f7bd917a0259, type: 2}
  enableAnimation: 1
  useCustomValues: 1
  customPropertyName: _AlphaCutoffValue
  customStartValue: 0.987
  customEndValue: 0.4
  customDuration: 12
  customEase: 6
  customDelay: 0
  resetOnDisable: 1
  enableDebugLogging: 0
--- !u!114 &8973770144313412763
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7671528889692876913}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9d36ca2ed9fc1124fa219fd9f6140f97, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::BreachEffectController
  materialAnimator: {fileID: -615261834649403541}
  radiusAnimator: {fileID: -4348043867358150448}
  enemySpawner: {fileID: -8365770041160165828}
  playOnPlayerTrigger: 1
  playOnEnable: 0
  enableDebugLogging: 0
--- !u!58 &6594840336060423115
CircleCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7671528889692876913}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0}
  m_Radius: 0.05
