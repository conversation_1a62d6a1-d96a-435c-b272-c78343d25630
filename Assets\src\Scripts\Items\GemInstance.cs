using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using Sirenix.OdinInspector;

[Serializable]
public class GemInstance
{
    [Title("Base Data")]
    [ShowInInspector, ReadOnly]
    public GemData gemDataTemplate;
    
    [ShowInInspector, ReadOnly]
    public string instanceId;
    
    [Title("Progression")]
    [ShowInInspector]
    public GemRarity rarity = GemRarity.Common;
    
    [ShowInInspector]
    public bool isCorrupted = false;
    
    [Title("Random Modifiers")]
    [ShowInInspector]
    [ListDrawerSettings(ShowIndexLabels = false, DraggableItems = false)]
    public List<SupportGemModifier> randomModifiers = new List<SupportGemModifier>();
    
    [Title("Debug Settings")]
    [SerializeField]
    [Tooltip("Enable debug logging for GemInstance calculations")]
    private bool enableGemInstanceDebugLogging = false;
    
    public GemInstance(GemData template, GemRarity gemRarity = GemRarity.Common, List<SupportGemModifier> modifiers = null)
    {
        gemDataTemplate = template;
        instanceId = Guid.NewGuid().ToString();
        rarity = gemRarity;
        isCorrupted = false;
        randomModifiers = modifiers ?? new List<SupportGemModifier>();
    }
    
    public GemInstance(GemInstance other)
    {
        gemDataTemplate = other.gemDataTemplate;
        instanceId = Guid.NewGuid().ToString();
        rarity = other.rarity;
        isCorrupted = other.isCorrupted;
        // Deep copy modifiers
        randomModifiers = new List<SupportGemModifier>();
        foreach (var mod in other.randomModifiers)
        {
            randomModifiers.Add(new SupportGemModifier(mod.type, mod.value, mod.tier));
        }
    }
    
    #region Calculated Properties
    
    public string DisplayName
    {
        get
        {
        if (gemDataTemplate == null) return "Unknown Gem";
        string name = gemDataTemplate.gemName;
        if (isCorrupted) name = "[Corrupted] " + name;
        return name;
        }
    }
    
    public Sprite Icon => gemDataTemplate?.GetIcon();
    
    public Color RarityColor => GetRarityColor();
    
    private Color GetRarityColor()
    {
        return RarityUtility.GetRarityColor(rarity);
    }
    
    public float GetDamageMultiplier()
    {
        return 1f;
    }
    
    public float GetCooldownMultiplier()
    {
        return 1f;
    }
    
    public float GetManaMultiplier()
    {
        return 1f;
    }
    
    #endregion
    
    #region Skill Gem Properties
    
    public bool IsSkillGem => gemDataTemplate is SkillGemData;
    public bool IsSupportGem => gemDataTemplate is SupportGemData;
    
    public float GetSkillDamage()
    {
        if (gemDataTemplate is SkillGemData skillGem)
        {
        return skillGem.baseDamage * GetDamageMultiplier();
        }
        return 0f;
    }
    
    public float GetSkillCooldown()
    {
        if (gemDataTemplate is SkillGemData skillGem)
        {
        return Mathf.Max(0.1f, skillGem.cooldown * GetCooldownMultiplier());
        }
        return 0f;
    }
    
    public float GetSkillManaCost()
    {
        if (gemDataTemplate is SkillGemData skillGem)
        {
        return skillGem.manaCost * GetManaMultiplier();
        }
        return 0f;
    }
    
    public int GetSupportSlotCount()
    {
        if (gemDataTemplate is SkillGemData skillGem)
        {
            // Use the configurable support slot system instead of hardcoded enum values
            return SupportSlotConfigurationManager.GetSupportSlotCount(rarity);
        }
        return 0;
    }
    
    #endregion
    
    #region Support Gem Properties
    
    /// <summary>
    /// Apply asymmetric scaling for positive and negative modifiers
    /// Positive modifiers get amplified with rarity, negative modifiers get reduced
    /// </summary>
    private float ApplyAsymmetricScaling(float baseValue, float rarityMultiplier)
    {
        if (baseValue >= 0f)
        {
            // Positive values: amplify with rarity (current behavior)
            return baseValue * rarityMultiplier;
        }
        else
        {
            // Negative values: reduce penalty with rarity (inverse scaling)
            // Higher rarity = less penalty
            return baseValue / rarityMultiplier;
        }
    }
    
    /// <summary>
    /// Apply asymmetric scaling for bonus values (used with multipliers)
    /// For multipliers like 1.3 (bonus = 0.3) or 0.7 (bonus = -0.3)
    /// </summary>
    private float ApplyAsymmetricBonusScaling(float bonusValue, float rarityMultiplier)
    {
        if (bonusValue >= 0f)
        {
            // Positive bonuses: amplify with rarity (current behavior)
            return bonusValue * rarityMultiplier;
        }
        else
        {
            // Negative bonuses: reduce penalty with rarity (inverse scaling)
            // Higher rarity = less penalty
            return bonusValue / rarityMultiplier;
        }
    }
    
    public float GetSupportDamageMultiplier()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to "more" damage multiplier
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            
            // Scale the bonus part of the multiplier with asymmetric scaling
            float baseBonus = supportGem.damageMore - 1f; // e.g., 1.3 -> 0.3, 0.7 -> -0.3
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            float scaledMultiplier = 1f + scaledBonus; // e.g., 1 + 0.3 = 1.3
            
            // Add random modifier bonuses (treated as "more" multipliers)
            float randomBonus = GetRandomModifierBonus(SupportGemModifierType.DamageMultiplier);
            
            // Convert percentage to multiplier (e.g., +20% = 1.2x)
            float totalMultiplier = scaledMultiplier * (1f + randomBonus / 100f);
            
            return totalMultiplier;
        }
        return 1f;
    }
    
    /// <summary>
    /// Get increased damage bonus from support gem (additive percentage)
    /// </summary>
    public float GetSupportIncreasedDamage()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Get base damage with asymmetric rarity scaling
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float scaledDamage = ApplyAsymmetricScaling(supportGem.damageIncreased, rarityMultiplier);
            
            // Return the scaled damage
            return scaledDamage;
            
            // Note: Random modifiers for "increased damage" could be added here
            // if we add a new SupportGemModifierType.DamageIncreased
        }
        return 0f;
    }
    
    /// <summary>
    /// Get type-specific increased damage modifiers with rarity scaling
    /// </summary>
    public float GetSupportPhysicalDamageIncreased()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyAsymmetricScaling(supportGem.physicalDamageIncreased, rarityMultiplier);
        }
        return 0f;
    }
    
    public float GetSupportFireDamageIncreased()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyAsymmetricScaling(supportGem.fireDamageIncreased, rarityMultiplier);
        }
        return 0f;
    }
    
    public float GetSupportIceDamageIncreased()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyAsymmetricScaling(supportGem.iceDamageIncreased, rarityMultiplier);
        }
        return 0f;
    }
    
    public float GetSupportLightningDamageIncreased()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyAsymmetricScaling(supportGem.lightningDamageIncreased, rarityMultiplier);
        }
        return 0f;
    }
    
    public float GetSupportElementalDamageIncreased()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyAsymmetricScaling(supportGem.elementalDamageIncreased, rarityMultiplier);
        }
        return 0f;
    }

    /// <summary>
    /// Get increased area of effect percentage from support gem
    /// </summary>
    public float GetSupportAreaIncreased()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyAsymmetricScaling(supportGem.areaIncreased, rarityMultiplier);
        }
        return 0f;
    }

    /// <summary>
    /// Get more area of effect multiplier from support gem
    /// </summary>
    public float GetSupportAreaMore()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.areaMore - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            return 1f + scaledBonus;
        }
        return 1f;
    }
    
    /// <summary>
    /// Get type-specific "more" damage multipliers with rarity scaling
    /// </summary>
    public float GetSupportPhysicalDamageMore()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.physicalDamageMore - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            return 1f + scaledBonus;
        }
        return 1f;
    }
    
    public float GetSupportFireDamageMore()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.fireDamageMore - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            return 1f + scaledBonus;
        }
        return 1f;
    }
    
    public float GetSupportIceDamageMore()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.iceDamageMore - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            return 1f + scaledBonus;
        }
        return 1f;
    }
    
    public float GetSupportLightningDamageMore()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.lightningDamageMore - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            return 1f + scaledBonus;
        }
        return 1f;
    }
    
    public float GetSupportElementalDamageMore()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.elementalDamageMore - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            float baseMultiplier = 1f + scaledBonus;
            
            // Apply elemental damage multipliers from random modifiers
            float randomModifierMultiplier = 1f;
            foreach (var modifier in randomModifiers)
            {
                if (modifier.type == SupportGemModifierType.ElementalDamageMultiplier)
                {
                    // ElementalDamageMultiplier is stored as percentage (e.g., 40 for 40% more)
                    // Convert to multiplier: 40% more = 1.4x
                    float modifierMultiplier = 1f + modifier.value / 100f;
                    randomModifierMultiplier *= modifierMultiplier;
                    
                    if (enableGemInstanceDebugLogging)
                        UnityEngine.Debug.Log($"[GemInstance] Applying ElementalDamageMultiplier from random modifier: {modifier.value}% more = {modifierMultiplier}x multiplier. Total random modifier multiplier: {randomModifierMultiplier}x");
                }
            }
            
            float finalMultiplier = baseMultiplier * randomModifierMultiplier;
            if (randomModifierMultiplier > 1f)
            {
                if (enableGemInstanceDebugLogging)
                    UnityEngine.Debug.Log($"[GemInstance] GetSupportElementalDamageMore - Base: {baseMultiplier}x, Random Modifiers: {randomModifierMultiplier}x, Final: {finalMultiplier}x");
            }
            
            // Combine base and random modifier multipliers
            return finalMultiplier;
        }
        return 1f;
    }
    
    public float GetSupportCooldownMultiplier()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float baseMultiplier = supportGem.cooldownMultiplier;
            
            // Add random modifier bonuses (cooldown reduction is negative)
            float randomReduction = GetRandomModifierBonus(SupportGemModifierType.CooldownReduction);
            
            // Apply reduction (e.g., -20% cooldown = 0.8x multiplier)
            float totalMultiplier = baseMultiplier * (1f - randomReduction / 100f);
            
            return Mathf.Max(0.1f, totalMultiplier); // Cap at 90% reduction
        }
        return 1f;
    }
    
    /// <summary>
    /// Get total bonus value for a specific modifier type from random modifiers
    /// </summary>
    public float GetRandomModifierBonus(SupportGemModifierType type)
    {
        return randomModifiers.Where(m => m.type == type).Sum(m => m.value);
    }
    
    /// <summary>
    /// Get support gem's added critical chance including random modifiers
    /// </summary>
    public float GetSupportCritChanceBonus()
    {
        float baseBonus = 0f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            baseBonus = supportGem.addedCritChance;
        }
        
        return baseBonus + GetRandomModifierBonus(SupportGemModifierType.CriticalChance);
    }
    
    /// <summary>
    /// Get support gem's critical multiplier modifier including random modifiers
    /// </summary>
    public float GetSupportCritMultiplierModifier()
    {
        float baseModifier = 1f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to crit multiplier with asymmetric scaling
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.critMultiplierModifier - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            baseModifier = 1f + scaledBonus;
        }
        
        // Apply random crit multiplier bonus
        float randomBonus = GetRandomModifierBonus(SupportGemModifierType.CriticalMultiplier);
        return baseModifier * (1f + randomBonus / 100f);
    }
    
    /// <summary>
    /// Get support gem's attack speed multiplier including random modifiers
    /// </summary>
    public float GetSupportAttackSpeedMultiplier()
    {
        float baseMultiplier = 1f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to attack speed with asymmetric scaling
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.attackSpeedMultiplier - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            baseMultiplier = 1f + scaledBonus;
        }
        
        // Apply random attack speed bonus
        float randomBonus = GetRandomModifierBonus(SupportGemModifierType.AttackSpeedMultiplier);
        return baseMultiplier * (1f + randomBonus / 100f);
    }
    
    /// <summary>
    /// Get support gem's skill duration multiplier including random modifiers
    /// </summary>
    public float GetSupportSkillDurationMultiplier()
    {
        float baseMultiplier = 1f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to skill duration with asymmetric scaling
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.skillDurationMultiplier - 1f;
            float scaledBonus = ApplyAsymmetricBonusScaling(baseBonus, rarityMultiplier);
            baseMultiplier = 1f + scaledBonus;
        }
        
        // Apply random skill duration bonus (if any modifier type is added later)
        // Note: Currently no random modifier for skill duration, but structure is ready
        return baseMultiplier;
    }
    
    /// <summary>
    /// Get total extra projectiles from random modifiers
    /// </summary>
    public int GetExtraProjectiles()
    {
        int baseExtra = 0;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsMultipleProjectiles)
        {
            // Apply rarity scaling
            float multiplier = supportGem.GetRarityMultiplier(rarity);
            if (supportGem.UsesIntegerScaling)
            {
                baseExtra = Mathf.FloorToInt(supportGem.extraProjectiles * multiplier);
            }
            else
            {
                baseExtra = supportGem.extraProjectiles; // No scaling for non-integer mode
            }
        }
        
        return baseExtra + Mathf.RoundToInt(GetRandomModifierBonus(SupportGemModifierType.ExtraProjectiles));
    }
    
    /// <summary>
    /// Get flat damage bonus from random modifiers
    /// </summary>
    public float GetSupportFlatDamageBonus()
    {
        return GetRandomModifierBonus(SupportGemModifierType.DamageFlat);
    }
    
    /// <summary>
    /// Get mana cost multiplier including random modifiers
    /// </summary>
    public float GetSupportManaCostMultiplier()
    {
        float baseMultiplier = 1f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to mana cost
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            // For mana cost, we want to scale the penalty/bonus appropriately
            if (supportGem.manaCostMultiplier > 1f)
            {
                // It's a penalty - scale it down for better rarities
                float penalty = supportGem.manaCostMultiplier - 1f;
                penalty /= rarityMultiplier; // Better rarity = less penalty
                baseMultiplier = 1f + penalty;
            }
            else
            {
                // It's a reduction - scale it up for better rarities
                float reduction = 1f - supportGem.manaCostMultiplier;
                reduction *= rarityMultiplier; // Better rarity = more reduction
                baseMultiplier = 1f - reduction;
            }
        }
        
        // Apply mana cost reduction (negative modifier)
        float randomReduction = GetRandomModifierBonus(SupportGemModifierType.ManaCostReduction);
        return baseMultiplier * (1f - randomReduction / 100f);
    }
    
    /// <summary>
    /// Get chain count including rarity scaling
    /// </summary>
    public int GetChainCount()
    {
        int baseChains = 0;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsChain)
        {
            // Apply rarity scaling
            float multiplier = supportGem.GetRarityMultiplier(rarity);
            if (supportGem.UsesIntegerScaling)
            {
                baseChains = Mathf.FloorToInt(supportGem.chainCount * multiplier);
            }
            else
            {
                baseChains = supportGem.chainCount; // No scaling for non-integer mode
            }
        }
        
        // Note: Could add random modifiers for chain count here if needed
        return baseChains;
    }
    
    /// <summary>
    /// Get area radius including rarity scaling
    /// DEPRECATED: Use GetSupportAreaIncreased() instead for percentage-based area modifiers
    /// </summary>
    [System.Obsolete("Use GetSupportAreaIncreased() for percentage-based area modifiers")]
    public float GetAreaRadius()
    {
        // Return 0 to indicate this method is deprecated
        // Area effects now use percentage-based increases via GetSupportAreaIncreased()
        return 0f;
    }
    
    /// <summary>
    /// Get fork count including rarity scaling
    /// </summary>
    public int GetForkCount()
    {
        int baseForks = 0;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsFork)
        {
            // Apply rarity scaling
            float multiplier = supportGem.GetRarityMultiplier(rarity);
            if (supportGem.UsesIntegerScaling)
            {
                baseForks = Mathf.FloorToInt(supportGem.forkCount * multiplier);
            }
            else
            {
                baseForks = supportGem.forkCount; // No scaling for non-integer mode
            }
        }
        
        return baseForks;
    }
    
    /// <summary>
    /// Get fork angle
    /// </summary>
    public float GetForkAngle()
    {
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsFork)
        {
            return supportGem.forkAngle;
        }
        
        return 30f; // Default fork angle
    }
    
    /// <summary>
    /// Get spell echo count including rarity scaling
    /// </summary>
    public int GetSpellEchoCount()
    {
        int baseEchoes = 0;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsSpellEcho)
        {
            // Apply rarity scaling if integer scaling is enabled
            if (supportGem.UsesIntegerScaling)
            {
                float multiplier = supportGem.GetRarityMultiplier(rarity);
                baseEchoes = Mathf.FloorToInt(supportGem.echoCount * multiplier);
            }
            else
            {
                baseEchoes = supportGem.echoCount;
            }
        }
        
        return baseEchoes;
    }

    /// <summary>
    /// Get autonomous range from support gem
    /// </summary>
    public float GetAutonomousRange()
    {
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsAutonomous)
        {
            return supportGem.autonomousRange;
        }
        return 0f;
    }

    /// <summary>
    /// Get armor penetration percentage with rarity scaling
    /// </summary>
    public float GetArmorPenetrationPercent()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyRarityScaling(supportGem.armorPenetrationPercent, rarityMultiplier);
        }
        return 0f;
    }

    /// <summary>
    /// Get flat armor penetration with rarity scaling
    /// </summary>
    public int GetFlatArmorPenetration()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            if (supportGem.UsesIntegerScaling)
            {
                return Mathf.FloorToInt(supportGem.flatArmorPenetration * rarityMultiplier);
            }
            else
            {
                return supportGem.flatArmorPenetration; // No scaling for non-integer mode
            }
        }
        return 0;
    }

    /// <summary>
    /// Get fire penetration with rarity scaling
    /// </summary>
    public float GetFirePenetration()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyRarityScaling(supportGem.firePenetration, rarityMultiplier);
        }
        return 0f;
    }

    /// <summary>
    /// Get cold penetration with rarity scaling
    /// </summary>
    public float GetColdPenetration()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyRarityScaling(supportGem.coldPenetration, rarityMultiplier);
        }
        return 0f;
    }

    /// <summary>
    /// Get lightning penetration with rarity scaling
    /// </summary>
    public float GetLightningPenetration()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyRarityScaling(supportGem.lightningPenetration, rarityMultiplier);
        }
        return 0f;
    }

    /// <summary>
    /// Get elemental penetration with rarity scaling
    /// </summary>
    public float GetElementalPenetration()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            return ApplyRarityScaling(supportGem.elementalPenetration, rarityMultiplier);
        }
        return 0f;
    }

    /// <summary>
    /// Apply rarity scaling to a stat value. Penetration is always beneficial, so uses normal scaling.
    /// </summary>
    private float ApplyRarityScaling(float baseValue, float rarityMultiplier)
    {
        // Penetration is always beneficial, so use normal scaling (not asymmetric)
        return baseValue * rarityMultiplier;
    }

    #endregion
    
    public string GetTooltipText()
    {
        if (gemDataTemplate == null) return "Unknown Gem";
        
        StringBuilder sb = new StringBuilder(512); // Pre-allocate reasonable capacity
        
        // Header
        sb.Append(TooltipFormatter.FormatGemHeader(DisplayName, rarity, IsSkillGem));
        
        // Add gem tags display
        if (IsSkillGem && gemDataTemplate is SkillGemData skillData && skillData.gemTags != GemTag.None)
        {
            var tagNames = new System.Collections.Generic.List<string>();
            if ((skillData.gemTags & GemTag.Melee) != 0) tagNames.Add(GemTag.Melee.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Projectile) != 0) tagNames.Add(GemTag.Projectile.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Spell) != 0) tagNames.Add(GemTag.Spell.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Duration) != 0) tagNames.Add(GemTag.Duration.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.AreaOfEffect) != 0) tagNames.Add(GemTag.AreaOfEffect.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Ailment) != 0) tagNames.Add(GemTag.Ailment.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Physical) != 0) tagNames.Add(GemTag.Physical.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Fire) != 0) tagNames.Add(GemTag.Fire.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Cold) != 0) tagNames.Add(GemTag.Cold.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Lightning) != 0) tagNames.Add(GemTag.Lightning.GetColoredDisplayName());
            sb.Append("\nTags: ");
            sb.Append(string.Join(", ", tagNames));
        }
        else if (IsSupportGem && gemDataTemplate is SupportGemData supportData && supportData.compatibleTags != GemTag.None)
        {
            var tagNames = new System.Collections.Generic.List<string>();
            if ((supportData.compatibleTags & GemTag.Melee) != 0) tagNames.Add(GemTag.Melee.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Projectile) != 0) tagNames.Add(GemTag.Projectile.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Spell) != 0) tagNames.Add(GemTag.Spell.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Duration) != 0) tagNames.Add(GemTag.Duration.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.AreaOfEffect) != 0) tagNames.Add(GemTag.AreaOfEffect.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Ailment) != 0) tagNames.Add(GemTag.Ailment.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Physical) != 0) tagNames.Add(GemTag.Physical.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Fire) != 0) tagNames.Add(GemTag.Fire.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Cold) != 0) tagNames.Add(GemTag.Cold.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Lightning) != 0) tagNames.Add(GemTag.Lightning.GetColoredDisplayName());
            sb.Append("\nCompatible with: ");
            sb.Append(string.Join(", ", tagNames));
        }
        
        if (!string.IsNullOrEmpty(gemDataTemplate.description))
        {
            sb.Append("\n\n");
            sb.Append(gemDataTemplate.description);
        }
        
        if (IsSkillGem && gemDataTemplate is SkillGemData skillStats)
        {
            sb.Append("\n\n");

            // Calculate damage - show Base → Final if there are modifiers
            float baseDamage = GetSkillDamage();
            float displayDamage = baseDamage;
            bool showBaseToFinal = false;
            
            // Apply all current modifiers internally
            var playerStats = PlayerManager.PlayerStats;
            if (playerStats != null)
            {
                // Use DamageCalculator for consistent damage calculation
                var emptyEffects = new SupportGemProcessor.AggregatedEffects(); // No support gems in inventory
                var damageBreakdown = DamageCalculator.Calculate(
                    baseDamage,
                    skillStats.damageType,
                    0f, // No flat Physical damage from support gems
                    emptyEffects,
                    playerStats
                );
                displayDamage = damageBreakdown.TotalDamage;
                
                // Show Base → Final if there's a difference
                if (Mathf.Abs(displayDamage - baseDamage) > 0.01f)
                {
                    showBaseToFinal = true;
                }
            }

            // Show damage in Base → Final format if there are modifiers
            if (showBaseToFinal)
            {
                sb.Append($"<color={TooltipFormatter.LABEL_COLOR}>Damage:</color> {baseDamage:F0} → <b>{displayDamage:F0}</b> {TooltipFormatter.FormatDamageType(skillStats.damageType)}\n");
            }
            else
            {
                sb.Append($"<color={TooltipFormatter.LABEL_COLOR}>Damage:</color> <b>{baseDamage:F0}</b> {TooltipFormatter.FormatDamageType(skillStats.damageType)}\n");
            }
            
            // Show other stats
            sb.Append(TooltipFormatter.FormatStat("Cooldown", GetSkillCooldown(), "F1", "s"));
            sb.Append("\n");
            sb.Append(TooltipFormatter.FormatStat("Mana Cost", GetSkillManaCost(), "F0"));
            sb.Append("\n");
            sb.Append(TooltipFormatter.FormatStat("Attack Speed", skillStats.attackSpeedMultiplier, "F2", "x"));
            sb.Append("\n");
            sb.Append(TooltipFormatter.FormatStat("Critical Chance", skillStats.critChance, "F1", "%"));
            sb.Append("\n");
            sb.Append(TooltipFormatter.FormatStat("Critical Multiplier", skillStats.critMultiplier, "F1", "x"));
            sb.Append("\n");
            sb.Append(TooltipFormatter.FormatStat("Support Gem Slots", GetSupportSlotCount(), "F0"));
            
            // Only show duration if the gem has the Duration tag
            if ((skillStats.gemTags & GemTag.Duration) != 0 && skillStats.duration > 0f)
            {
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Duration", skillStats.duration, "F1", "s"));
            }

            // Add status effect information using new breakdown-aware method
            if (skillStats.ailmentChance > 0)
            {
                // Create breakdown using the same damage calculation as above (includes PlayerStats)
                var simpleBreakdown = new DamageBreakdown(displayDamage, skillStats.damageType);

                // Create status effect config from skill gem data
                var statusConfig = new StatusEffectHelper.StatusEffectConfig
                {
                    ignitePercent = skillStats.ignitePercent,
                    igniteDuration = skillStats.igniteDuration,
                    igniteTickInterval = skillStats.igniteTickInterval,
                    freezeSlowAmount = skillStats.freezeSlowAmount,
                    freezeDuration = skillStats.freezeDuration,
                    bleedPercent = skillStats.bleedPercent,
                    bleedDuration = skillStats.bleedDuration,
                    bleedTickInterval = skillStats.bleedTickInterval,
                    shockChainDamage = skillStats.shockChainDamage,
                    shockChainRange = skillStats.shockChainRange,
                    shockDuration = skillStats.shockDuration
                };

                string ailmentInfo = TooltipFormatter.FormatAilmentChances(simpleBreakdown, skillStats.ailmentChance, statusConfig);
                if (!string.IsNullOrEmpty(ailmentInfo))
                {
                    sb.Append("\n\n");
                    sb.Append(ailmentInfo);
                }
            }
        }
        else if (IsSupportGem && gemDataTemplate is SupportGemData supportData)
        {
            // ===== PRIMARY EFFECTS SECTION =====
            // Show core mechanical changes first (conversion, echo, pierce, etc.)
            bool hasPrimaryEffects = false;
            
            // Damage Conversion (most important mechanical change)
            if (supportData.isConversionGem)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Damage Conversion", 
                    $"{supportData.conversionPercent:F0}% {TooltipFormatter.FormatDamageType(supportData.convertFromType)} → {TooltipFormatter.FormatDamageType(supportData.convertToType)}"));
            }
            
            // Spell Echo
            if (supportData.addsSpellEcho)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                int echoCount = GetSpellEchoCount();
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatSpecialEffect("Spell Echo", $"Recasts {echoCount} time{(echoCount > 1 ? "s" : "")}"));
                if (supportData.echoSpreadRadius > 0)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatSpecialEffect("Echo Spread", $"Radius {supportData.echoSpreadRadius}"));
                }
                if (supportData.echoDelay > 0)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatSpecialEffect("Echo Delay", $"{supportData.echoDelay:F2}s"));
                }
            }
            
            // Projectile modifications
            if (supportData.addsPierce)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatSpecialEffect("Pierce", "Projectiles pierce all enemies"));
            }
            if (supportData.addsChain)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                int chainCount = GetChainCount();
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatSpecialEffect("Chain", $"+{chainCount} time{(chainCount > 1 ? "s" : "")}"));
            }
            if (supportData.addsFork)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                int forkCount = GetForkCount();
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatSpecialEffect("Fork", $"Splits into {forkCount} projectiles ({supportData.forkAngle}° spread)"));
            }
            if (supportData.addsMultipleProjectiles)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                int totalProjectiles = GetExtraProjectiles();
                string projectileText = $"+{totalProjectiles} projectile{(totalProjectiles > 1 ? "s" : "")}";
                if (supportData.projectileSpreadAngle > 0)
                    projectileText += $" ({supportData.projectileSpreadAngle}° spread)";
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatSpecialEffect("Additional Projectiles", projectileText));
            }
            if (supportData.addsAreaDamage)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                float areaRadius = GetAreaRadius();
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatSpecialEffect("Area Damage", $"Radius {areaRadius:F1}"));
            }
            
            // Special mechanics
            if (supportData.preventsElementalDamage)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                sb.Append("\n");
                sb.Append("<color=#FF6B6B>Prevents all Elemental Damage</color>");
            }
            if (supportData.addsRuthlessHits)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatSpecialEffect("Ruthless Hits", 
                    $"Every {supportData.ruthlessHitInterval} hits deals {(supportData.ruthlessDamageMultiplier - 1f) * 100:+0}% more damage"));
            }
            if (supportData.addsAutonomous)
            {
                if (!hasPrimaryEffects) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Primary Effects")); hasPrimaryEffects = true; }
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatSpecialEffect("Autonomous Casting", $"Auto-targets enemies within {GetAutonomousRange()} range"));
            }
            
            // Add note for conversion gems
            if (supportData.isConversionGem)
            {
                sb.Append("\n<color=");
                sb.Append(TooltipFormatter.LABEL_COLOR);
                sb.Append("><i>Only one conversion support gem can be equipped per skill</i></color>");
            }
            
            // ===== OFFENSIVE STATS SECTION =====
            bool hasOffensiveStats = false;
            
            // General damage modifiers
            float actualIncreasedDamage = GetSupportIncreasedDamage();
            if (actualIncreasedDamage != 0f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Increased All Damage", $"{actualIncreasedDamage:+0;-0}%"));
            }
            float actualDamageMultiplier = GetSupportDamageMultiplier();
            if (actualDamageMultiplier != 1f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                float pct = (actualDamageMultiplier - 1f) * 100f;
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("More All Damage", $"{pct:+0;-0}%"));
            }
            
            // Type-specific damage modifiers
            if (supportData.physicalDamageIncreased != 0f || supportData.physicalDamageMore != 1f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                if (supportData.physicalDamageIncreased != 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Physical Damage Increased", $"{GetSupportPhysicalDamageIncreased():+0;-0}%"));
                }
                if (supportData.physicalDamageMore != 1f)
                {
                    float pct = (GetSupportPhysicalDamageMore() - 1f) * 100f;
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Physical Damage More", $"{pct:+0;-0}%"));
                }
            }
            if (supportData.fireDamageIncreased != 0f || supportData.fireDamageMore != 1f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                if (supportData.fireDamageIncreased != 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Fire Damage Increased", $"{GetSupportFireDamageIncreased():+0;-0}%"));
                }
                if (supportData.fireDamageMore != 1f)
                {
                    float pct = (GetSupportFireDamageMore() - 1f) * 100f;
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Fire Damage More", $"{pct:+0;-0}%"));
                }
            }
            if (supportData.iceDamageIncreased != 0f || supportData.iceDamageMore != 1f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                if (supportData.iceDamageIncreased != 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Ice Damage Increased", $"{GetSupportIceDamageIncreased():+0;-0}%"));
                }
                if (supportData.iceDamageMore != 1f)
                {
                    float pct = (GetSupportIceDamageMore() - 1f) * 100f;
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Ice Damage More", $"{pct:+0;-0}%"));
                }
            }
            if (supportData.lightningDamageIncreased != 0f || supportData.lightningDamageMore != 1f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                if (supportData.lightningDamageIncreased != 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Lightning Damage Increased", $"{GetSupportLightningDamageIncreased():+0;-0}%"));
                }
                if (supportData.lightningDamageMore != 1f)
                {
                    float pct = (GetSupportLightningDamageMore() - 1f) * 100f;
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Lightning Damage More", $"{pct:+0;-0}%"));
                }
            }
            if (supportData.elementalDamageIncreased != 0f || supportData.elementalDamageMore != 1f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                if (supportData.elementalDamageIncreased != 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Elemental Damage Increased", $"{GetSupportElementalDamageIncreased():+0;-0}%"));
                }
                if (supportData.elementalDamageMore != 1f)
                {
                    float pct = (GetSupportElementalDamageMore() - 1f) * 100f;
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Elemental Damage More", $"{pct:+0;-0}%"));
                }
            }
            
            // Attack speed and critical modifiers
            float actualAttackSpeedMultiplier = GetSupportAttackSpeedMultiplier();
            if (actualAttackSpeedMultiplier != 1f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                float pct = (actualAttackSpeedMultiplier - 1f) * 100f;
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Attack Speed", $"{pct:+0;-0}%"));
            }
            float actualCritChanceBonus = GetSupportCritChanceBonus();
            if (actualCritChanceBonus != 0f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Critical Chance", $"+{actualCritChanceBonus:F1}%"));
            }
            float actualCritMultiplier = GetSupportCritMultiplierModifier();
            if (actualCritMultiplier != 1f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                float pct = (actualCritMultiplier - 1f) * 100f;
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Critical Multiplier", $"{pct:+0;-0}%"));
            }
            
            // Flat damage bonus (from random modifiers)
            float flatDamageBonus = GetSupportFlatDamageBonus();
            if (flatDamageBonus != 0f)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Flat Physical Damage", $"+{flatDamageBonus:F0}"));
            }

            // Penetration stats
            bool hasPenetration = GetArmorPenetrationPercent() > 0f || GetFlatArmorPenetration() > 0 ||
                                 GetFirePenetration() > 0f || GetColdPenetration() > 0f || 
                                 GetLightningPenetration() > 0f || GetElementalPenetration() > 0f;
            
            if (hasPenetration)
            {
                if (!hasOffensiveStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Offensive Stats")); hasOffensiveStats = true; }
                
                // Armor penetration
                if (GetArmorPenetrationPercent() > 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Armor Penetration", $"{GetArmorPenetrationPercent():F1}%"));
                }
                if (GetFlatArmorPenetration() > 0)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Flat Armor Penetration", GetFlatArmorPenetration().ToString()));
                }
                
                // Elemental penetration
                if (GetFirePenetration() > 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Fire Penetration", $"{GetFirePenetration():F1}%"));
                }
                if (GetColdPenetration() > 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Cold Penetration", $"{GetColdPenetration():F1}%"));
                }
                if (GetLightningPenetration() > 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Lightning Penetration", $"{GetLightningPenetration():F1}%"));
                }
                if (GetElementalPenetration() > 0f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatStat("Elemental Penetration", $"{GetElementalPenetration():F1}%"));
                }
            }
            
            // ===== UTILITY STATS SECTION =====
            bool hasUtilityStats = false;
            
            // Resource costs
            float actualCooldownMultiplier = GetSupportCooldownMultiplier();
            if (actualCooldownMultiplier != 1f)
            {
                if (!hasUtilityStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Utility Stats")); hasUtilityStats = true; }
                float pct = (actualCooldownMultiplier - 1f) * 100f;
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Cooldown", $"{pct:+0;-0}%"));
            }
            float actualManaCostMultiplier = GetSupportManaCostMultiplier();
            if (actualManaCostMultiplier != 1f)
            {
                if (!hasUtilityStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Utility Stats")); hasUtilityStats = true; }
                float pct = (actualManaCostMultiplier - 1f) * 100f;
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Mana Cost", $"{pct:+0;-0}%"));
            }
            float actualSkillDurationMultiplier = GetSupportSkillDurationMultiplier();
            if (actualSkillDurationMultiplier != 1f)
            {
                if (!hasUtilityStats) { sb.Append("\n\n"); sb.Append(TooltipFormatter.FormatSectionHeader("Utility Stats")); hasUtilityStats = true; }
                float pct = (actualSkillDurationMultiplier - 1f) * 100f;
                sb.Append("\n");
                sb.Append(TooltipFormatter.FormatStat("Skill Duration", $"{pct:+0;-0}%"));
            }
            
            // ===== STATUS EFFECTS SECTION =====
            bool hasStatusEffectModifiers = supportData.igniteEffectivenessMultiplier != 1f || supportData.igniteDurationMultiplier != 1f ||
                                           supportData.freezeEffectivenessMultiplier != 1f || supportData.freezeDurationMultiplier != 1f ||
                                           supportData.bleedEffectivenessMultiplier != 1f || supportData.bleedDurationMultiplier != 1f ||
                                           supportData.shockEffectivenessMultiplier != 1f || supportData.shockRangeMultiplier != 1f;

            if (hasStatusEffectModifiers)
            {
                sb.Append("\n\n");
                sb.Append(TooltipFormatter.FormatSectionHeader("Status Effects"));
                
                // Ignite modifiers
                if (supportData.igniteEffectivenessMultiplier != 1f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatPercentageModifier("Ignite Damage", supportData.igniteEffectivenessMultiplier));
                }
                if (supportData.igniteDurationMultiplier != 1f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatPercentageModifier("Ignite Duration", supportData.igniteDurationMultiplier));
                }

                // Freeze modifiers
                if (supportData.freezeEffectivenessMultiplier != 1f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatPercentageModifier("Freeze Effectiveness", supportData.freezeEffectivenessMultiplier));
                }
                if (supportData.freezeDurationMultiplier != 1f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatPercentageModifier("Freeze Duration", supportData.freezeDurationMultiplier));
                }

                // Bleed modifiers
                if (supportData.bleedEffectivenessMultiplier != 1f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatPercentageModifier("Bleed Damage", supportData.bleedEffectivenessMultiplier));
                }
                if (supportData.bleedDurationMultiplier != 1f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatPercentageModifier("Bleed Duration", supportData.bleedDurationMultiplier));
                }

                // Shock modifiers
                if (supportData.shockEffectivenessMultiplier != 1f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatPercentageModifier("Shock Damage", supportData.shockEffectivenessMultiplier));
                }
                if (supportData.shockRangeMultiplier != 1f)
                {
                    sb.Append("\n");
                    sb.Append(TooltipFormatter.FormatPercentageModifier("Shock Range", supportData.shockRangeMultiplier));
                }
            }
        }

        // Add unlock section for locked gems
        string unlockSection = TooltipFormatter.FormatUnlockSection(gemDataTemplate);
        if (!string.IsNullOrEmpty(unlockSection))
        {
            sb.Append(unlockSection);
        }

        if (isCorrupted)
        {
            sb.Append("\n\n");
            sb.Append(TooltipFormatter.FormatCorruptionStatus());
        }
        
        return sb.ToString();
    }
    
    #region Equality
    
    public override bool Equals(object obj)
    {
        if (obj is GemInstance other)
        {
        return instanceId == other.instanceId;
        }
        return false;
    }
    
    public override int GetHashCode()
    {
        return instanceId?.GetHashCode() ?? 0;
    }
    
    #endregion
}