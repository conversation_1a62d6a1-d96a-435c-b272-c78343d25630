using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Strategy implementation for AOE projectile-based skills.
/// Handles AOE projectile spawning with proper damage breakdown and support gem integration.
/// Maintains zero-GC performance while adding explosive projectile functionality.
/// </summary>
public class AOEProjectileSkillExecutor : ISkillExecutor
{
    public bool CanExecute(SkillType skillType) => skillType == SkillType.AOEProjectile;

    public void Execute(SkillExecutor skillExecutor, int slotIndex, GemSocketController controller,
                       SkillGemData skillData, Vector3 targetPosition, bool isAutonomous)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        skillExecutor.CacheSkillExecutionValues(controller, slotIndex);

        // Calculate direction using cached vectors
        var cachedDirectionVector = skillExecutor._cachedDirectionVector;
        cachedDirectionVector = (targetPosition - skillExecutor.transform.position).normalized;
        float baseAngle = Mathf.Atan2(cachedDirectionVector.y, cachedDirectionVector.x) * Mathf.Rad2Deg;

        // Get projectile count and spread (these are relatively cheap)
        int projectileCount = controller.GetTotalProjectileCount();
        bool useParallel = controller.UseParallelProjectiles();
        
        // Access execution cache for cached values
        var executionCache = skillExecutor.GetExecutionCache(slotIndex);
        
        // Spawn multiple AOE projectiles
        for (int i = 0; i < projectileCount; i++)
        {
            Vector3 spawnPosition;
            Quaternion rotation;
            Vector2 direction;

            if (useParallel)
            {
                // Parallel projectiles - calculate lateral offset
                float lateralOffset = controller.GetProjectileLateralOffset();
                float totalWidth = (projectileCount - 1) * lateralOffset;
                float offsetAmount = -totalWidth / 2f + (i * lateralOffset);

                // Calculate perpendicular vector for offset using cached vector
                var cachedPerpendicularVector = skillExecutor._cachedPerpendicularVector;
                cachedPerpendicularVector.Set(-cachedDirectionVector.y, cachedDirectionVector.x);
                cachedPerpendicularVector *= offsetAmount;
                spawnPosition = skillExecutor.transform.position + (Vector3)cachedPerpendicularVector;

                // All parallel projectiles use the same angle and direction
                rotation = Quaternion.Euler(0, 0, baseAngle);
                direction = cachedDirectionVector;
            }
            else
            {
                // Angular spread behavior
                float spreadAngle = controller.GetProjectileSpreadAngle();
                float currentAngle;
                
                if (controller.UseRandomSpread())
                {
                    // Random spread within the spread angle - like Spark in PoE
                    float randomOffset = Random.Range(-spreadAngle / 2f, spreadAngle / 2f);
                    currentAngle = baseAngle + randomOffset;
                }
                else
                {
                    // Even distribution (original behavior)
                    float angleStep = projectileCount > 1 ? spreadAngle / (projectileCount - 1) : 0;
                    float startAngle = baseAngle - (spreadAngle / 2f);
                    currentAngle = startAngle + (angleStep * i);
                }

                rotation = Quaternion.Euler(0, 0, currentAngle);
                float radians = currentAngle * Mathf.Deg2Rad;
                // Use cached vector for direction calculation
                var cachedVector2Temp = skillExecutor._cachedVector2Temp;
                cachedVector2Temp.Set(Mathf.Cos(radians), Mathf.Sin(radians));
                direction = cachedVector2Temp;
                spawnPosition = skillExecutor.transform.position;
            }
            
            GameObject projectileObj = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, rotation);
            if (projectileObj == null) continue;
            
            // Configure AOE projectile using cached values
            if (PoolManager.Instance.GetCachedComponent<AOEProjectile>(projectileObj, out var aoeProjectile))
            {
                // Validate cached damage to prevent 0 damage issue
                float finalDamage = executionCache.playerModifiedDamage;
                if (finalDamage <= 0f)
                {
                    // Recalculate if cache is invalid
                    skillExecutor.CacheSkillExecutionValues(controller, slotIndex);
                    executionCache = skillExecutor.GetExecutionCache(slotIndex);
                    finalDamage = executionCache.playerModifiedDamage;
                    
                    if (finalDamage <= 0f)
                    {
                        // Fallback to base damage if still invalid
                        finalDamage = skillData.baseDamage;
                    }
                }

                // Use cached values instead of expensive method calls
                aoeProjectile.damage = finalDamage;
                aoeProjectile.speed = skillData.projectileSpeed;
                aoeProjectile.lifetime = skillData.duration;
                aoeProjectile.critChance = executionCache.finalCritChance;
                aoeProjectile.critMultiplier = executionCache.finalCritMultiplier;
                aoeProjectile.damageType = executionCache.damageBreakdown.GetPredominantType(); // Use converted type!
                aoeProjectile.ailmentChance = skillData.ailmentChance;
                
                // Ensure we have a valid damage breakdown
                if (executionCache.damageBreakdown.TotalDamage <= 0f)
                {
                    Debug.LogWarning($"[AOEProjectileSkillExecutor] Skill '{skillData.gemName}' has invalid damage breakdown! TotalDamage: {executionCache.damageBreakdown.TotalDamage}, FinalDamage: {finalDamage}, BaseDamage: {skillData.baseDamage}. Creating enhanced fallback breakdown.");
                    
                    // Try to preserve support gem effects by recalculating with current support gems
                    var supportGems = controller.GetCompatibleSupportGems();
                    if (supportGems != null && supportGems.Count > 0)
                    {
                        // Recalculate with support gems to preserve conversion and modifiers
                        var effects = SupportGemProcessor.ProcessSupportGemsZeroGC(supportGems);
                        var recalculatedBreakdown = DamageCalculator.Calculate(skillData.baseDamage, skillData.damageType, effects, PlayerManager.PlayerStats);
                        
                        if (recalculatedBreakdown.TotalDamage > 0f)
                        {
                            // Scale the recalculated breakdown to match the final damage
                            float scaleFactor = finalDamage / recalculatedBreakdown.TotalDamage;
                            recalculatedBreakdown.ScaleAllDamage(scaleFactor);
                            aoeProjectile.damageBreakdown = recalculatedBreakdown;
                        }
                        else
                        {
                            // Last resort: use predominant type from cache or base type
                            var predominantType = executionCache.damageBreakdown.GetPredominantType();
                            if (predominantType == default) predominantType = skillData.damageType;
                            
                            var fallbackBreakdown = new DamageBreakdown(finalDamage, predominantType);
                            aoeProjectile.damageBreakdown = fallbackBreakdown;
                        }
                        
                        // Return pooled effects to prevent memory leaks
                        effects.ReturnToPool();
                    }
                    else
                    {
                        // No support gems: use base damage type
                        var fallbackBreakdown = new DamageBreakdown(finalDamage, skillData.damageType);
                        aoeProjectile.damageBreakdown = fallbackBreakdown;
                    }
                }
                else
                {
                    aoeProjectile.damageBreakdown = executionCache.damageBreakdown; // Pass full breakdown!
                }

                // Pass gem data for status effect configuration
                aoeProjectile.skillGemData = skillData;
                aoeProjectile.supportGems = skillExecutor.GetCachedSupportGems(controller, slotIndex);

                // Configure AOE-specific parameters based on support gems
                float aoeRadius = 3.0f; // Base AOE radius
                float aoeDamageMultiplier = 0.7f; // Base AOE damage multiplier
                bool triggerOnHit = true;
                bool triggerOnTimeout = false;

                // Apply support gem modifiers for AOE
                var supportGemList = aoeProjectile.supportGems;
                if (supportGemList != null)
                {
                    // Calculate total area increased percentage
                    float totalAreaIncreased = 0f;

                    foreach (var gem in supportGemList)
                    {
                        var supportGem = gem.gemDataTemplate as SupportGemData;
                        if (supportGem != null && supportGem.addsAreaDamage)
                        {
                            // Area modifiers - use percentage-based increases
                            float areaIncreased = gem.GetSupportAreaIncreased();
                            totalAreaIncreased += areaIncreased;

                            // Area damage modifiers - use support gem damage multipliers
                            float damageMultiplier = gem.GetSupportDamageMultiplier();
                            if (damageMultiplier != 1f)
                                aoeDamageMultiplier *= damageMultiplier;
                        }
                    }

                    // Apply area percentage modifier
                    if (totalAreaIncreased != 0f)
                    {
                        aoeRadius *= (1f + totalAreaIncreased / 100f);
                    }
                }

                // Set AOE parameters
                aoeProjectile.SetAOEParameters(aoeRadius, aoeDamageMultiplier, triggerOnHit, triggerOnTimeout);

                // AOE projectiles typically don't use standard support gem effects like pierce/chain/fork
                // but we can still apply them if the support gems are configured for it
                if (executionCache.hasPierce)
                {
                    aoeProjectile.SetPiercing(true);
                }

                if (executionCache.hasChain)
                {
                    aoeProjectile.SetChaining(true, executionCache.chainCount);
                }

                if (executionCache.hasFork)
                {
                    aoeProjectile.SetFork(true, executionCache.forkCount, executionCache.forkAngle);
                }

                // Note: Area damage is handled by the AOE explosion, not the standard area damage system
                // So we don't need to call SetAreaDamage here

                // Initialize projectile with cached damage (pass actual damage value, not multiplier)
                int layer = skillData.ProjectileLayer; // Use gem's collision layer (auto-migrates old values)
                aoeProjectile.Initialize((Vector2)spawnPosition, direction, finalDamage, layer, skillData.projectileSpeed, skillData.duration);
                
                // Apply speed variation AFTER Initialize() to prevent override (only for multi-projectile skills)
                if (skillData.intrinsicProjectileCount > 1 && skillData.projectileSpeedVariation > 0f)
                {
                    float speedVariation = Random.Range(-skillData.projectileSpeedVariation, skillData.projectileSpeedVariation);
                    aoeProjectile.speed = Mathf.Max(1f, skillData.projectileSpeed + speedVariation);
                }
                
                // Double-check damage after initialization
                if (aoeProjectile.damage <= 0f)
                {
                    Debug.LogError($"[AOEProjectileSkillExecutor] AOE Projectile damage is 0 after initialization! Setting to fallback: {skillData.baseDamage}");
                    aoeProjectile.damage = skillData.baseDamage;
                }
            }
            else
            {
                // Fallback to regular projectile if AOEProjectile component is not found
                Debug.LogWarning($"[AOEProjectileSkillExecutor] Skill '{skillData.gemName}' prefab doesn't have AOEProjectile component! Falling back to regular projectile behavior.");
                
                if (PoolManager.Instance.GetCachedComponent<Projectile>(projectileObj, out var regularProjectile))
                {
                    // Configure as regular projectile with same logic as ProjectileSkillExecutor
                    float finalDamage = executionCache.playerModifiedDamage;
                    if (finalDamage <= 0f)
                    {
                        finalDamage = skillData.baseDamage;
                    }

                    regularProjectile.damage = finalDamage;
                    regularProjectile.speed = skillData.projectileSpeed;
                    regularProjectile.lifetime = skillData.duration;
                    regularProjectile.critChance = executionCache.finalCritChance;
                    regularProjectile.critMultiplier = executionCache.finalCritMultiplier;
                    regularProjectile.damageType = executionCache.damageBreakdown.GetPredominantType();
                    regularProjectile.ailmentChance = skillData.ailmentChance;
                    regularProjectile.damageBreakdown = executionCache.damageBreakdown;
                    regularProjectile.skillGemData = skillData;
                    regularProjectile.supportGems = skillExecutor.GetCachedSupportGems(controller, slotIndex);

                    // Apply support gem effects
                    if (executionCache.hasPierce)
                        regularProjectile.SetPiercing(true);
                    if (executionCache.hasChain)
                        regularProjectile.SetChaining(true, executionCache.chainCount);
                    if (executionCache.hasFork)
                        regularProjectile.SetFork(true, executionCache.forkCount, executionCache.forkAngle);
                    if (executionCache.hasAreaDamage)
                        regularProjectile.SetAreaDamage(true, executionCache.areaRadius);

                    int layer = skillData.ProjectileLayer;
                    regularProjectile.Initialize((Vector2)spawnPosition, direction, finalDamage, layer, skillData.projectileSpeed, skillData.duration);
                }
            }
        }
    }
}