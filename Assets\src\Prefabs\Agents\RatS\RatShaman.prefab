%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &4037525124044918771
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1017452886696441335}
  m_Layer: 0
  m_Name: Spawn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1017452886696441335
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4037525124044918771}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.085, y: 0.551, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6069023758706627798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6482766385445591336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6069023758706627798}
  - component: {fileID: 8486459014864959627}
  - component: {fileID: 6758212963008494185}
  - component: {fileID: 5945171977097501345}
  - component: {fileID: 1456700642579193807}
  - component: {fileID: 6930833854545776605}
  - component: {fileID: 4937357421361664338}
  - component: {fileID: 5149457472067251805}
  - component: {fileID: 6649083756395347113}
  - component: {fileID: 5054299494717311555}
  - component: {fileID: 8003740787519023754}
  - component: {fileID: 725538249661335736}
  m_Layer: 0
  m_Name: RatShaman
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6069023758706627798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1017452886696441335}
  - {fileID: 969109904267124478}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &8486459014864959627
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ad694b2fc2accd42b37375d3b8a6f44, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 1546342031
  m_SortingLayer: 1
  m_SortingOrder: 10
  m_Sprite: {fileID: 981802721, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 0.5}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!114 &6758212963008494185
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dcda6c75a7fde1b4aa9e783b4e678706, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::Systems.Animation.SpriteAnimator
  defaultAnimation: {fileID: 11400000, guid: 4e96d7c9c7d37114597f15e839f9c7e0, type: 2}
  playOnStart: 1
  globalSpeedMultiplier: 1
  rendererMode: 2
  manualRenderers:
  - {fileID: 8486459014864959627}
  - {fileID: 253337148338995042}
  includeInactiveChildren: 0
  childTagFilter: 
  animations:
  - animation: {fileID: 11400000, guid: a54354c59f285804ea87bb177d0da1ac, type: 2}
  - animation: {fileID: 11400000, guid: 0fea7de1eb2060a4b80a203574aa97ba, type: 2}
--- !u!114 &5945171977097501345
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e0160eafd554d1748adf68108281db20, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Systems.CombatantHealth
  _maxHealth: 100
  m_useHitSplatter: 1
  m_useDeathSplatter: 1
  baseDefensiveStats: {fileID: 0}
  allowRuntimeDefensiveOverride: 0
  useZoneBasedDefensiveScaling: 1
  enableDebugLogging: 0
  useHitFlash: 1
  hitFlashDuration: 0.1
  hitFlashColor: {r: 1, g: 0.3, b: 0.3, a: 1}
  smoothFlashFade: 0
  flashFadeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  effectTransitionSpeed: 2
  useHitParticles: 0
  hitParticleType: 0
  hitParticleCount: 15
  useDirectionalParticles: 1
  allowStatusEffectBlood: 1
  statusEffectBloodChance: 0.15
  enableBloodDebugLogging: 0
  useDeathParticles: 0
  deathParticleType: 0
  deathParticleCount: 30
  deathDelay: 0.1
  returnToPoolOnDeath: 1
  particleSpawnPoint: {fileID: 0}
  spriteRenderer: {fileID: 8486459014864959627}
--- !u!114 &1456700642579193807
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 316b1ee554b20084a985f5763b7883ce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Enemy.Movement.EnemyAnimationController
  idleAnimationName: idle
  moveAnimationName: move
  chaseAnimationSpeed: 1.5
  velocityThreshold: 0.1
  animationBlendSpeed: 0.5
--- !u!114 &6930833854545776605
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc670c65382f04443a53fb5689817215, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Enemy.Movement.EnemyChunkBoundaryEnforcer
  chunkEdgeStopDistance: 1
  edgeReturnDelay: 2
  enableDebugLogging: 0
--- !u!114 &4937357421361664338
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fc4e8a2111ee4ee4cbc86f2722e74e2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Enemy.Movement.EnemyDebugVisualizer
  showDebugGizmos: 0
  enableDebugLogging: 0
  detectionRange: 8
  loseTargetRange: 12
  attackRange: 1.5
  patrolRadius: 5
--- !u!114 &5149457472067251805
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfe9431ea8ad072f2aecd3041b1524dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: AstarPathfindingProject::Pathfinding.FollowerEntity
  version: 1073741831
  shape:
    radius: 0.3
    height: 0.2
  movement:
    follower:
      rotationSpeed: 600
      speed: 5
      maxRotationSpeed: 720
      maxOnSpotRotationSpeed: 720
      slowdownTime: 0.5
      slowdownTimeWhenTurningOnSpot: 0
      desiredWallDistance: 0.5
      leadInRadiusWhenApproachingDestination: 1
      allowRotatingOnSpotBacking: 1
    debugFlags: 64
    stopDistance: 0.2
    rotationSmoothing: 0
    positionSmoothing: 0
    groundMask:
      serializedVersion: 2
      m_Bits: 4294967295
    isStopped: 0
  managedState:
    rvoSettings:
      agentTimeHorizon: 1
      obstacleTimeHorizon: 0.5
      maxNeighbours: 10
      layer: 1
      collidesWith: -1
      priority: 0.5
      debug: 0
      locked: 0
    pathfindingSettings:
      graphMask:
        value: -1
      tagPenalties: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
      traversableTags: -1
    enableLocalAvoidance: 1
    enableGravity: 1
  autoRepathBacking:
    mode: 2
    period: 2
  orientationBacking: 1
  movementPlaneSourceBacking: 0
  syncPosition: 1
  syncRotation: 0
--- !u!114 &6649083756395347113
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 014034c85a6de694c94e53855019ae8a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Enemy.Movement.PathfindingMovement
  autoFlipSprite: 1
  flipThreshold: 0.01
  initialState: 1
  detectionRange: 8
  loseTargetRange: 12
  idleDurationRange: {x: 2, y: 5}
  patrolRadius: 5
  waypointReachedDistance: 0.5
  patrolIdleDurationRange: {x: 1, y: 3}
  stateUpdateInterval: 0.5
--- !u!114 &5054299494717311555
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7c9b1dcfd8785914cb7af08c3f127086, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Enemy.PathfindingEnemy
  enemyType: 0
  attackStrategy:
    rid: 2739021002808754561
  experienceReward: 10
  experienceDistanceScaling: 0.1
  enableIndependentCurrencyDrops: 0
  independentCurrencyConfig: {fileID: 0}
  independentCurrencyDropChance: 0.25
  targetSelectionMode: 2
  preferredTargetTypes: 00000000
  showTargetDebug: 0
  enableEnemyDebugLogging: 0
  spriteRenderer: {fileID: 8486459014864959627}
  references:
    version: 2
    RefIds:
    - rid: 2739021002808754561
      type: {class: RangedAttackStrategy, ns: , asm: Assembly-CSharp}
      data:
        attackType: 1
        damage: 15
        attackRange: 5
        attackCooldown: 2
        projectileLayer: 8
        projectileSpeed: 10
        projectilePrefab: {fileID: 2340940985034927489, guid: 86c4600fd094c2140a6b8002f9afb294, type: 3}
        instantSpellPrefab: {fileID: 2340940985034927489, guid: 5ba2af05a908d6f4b8a4ba375d31dd00, type: 3}
        multiSpellSpreadRadius: 2
        spawnPoint: {fileID: 1017452886696441335}
        animationName: attack
        requiresAnimation: 1
        enableDebugLogs: 0
--- !u!114 &8003740787519023754
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e8218cc0a139c8343aa32a30684e517b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::YSortingController
  yOffset: 0
  updateInterval: 0.3
  baseSortingOrder: 0
  sortingPrecision: 100
  useFrustumCheck: 1
  frustumMargin: 1
--- !u!58 &725538249661335736
CircleCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6482766385445591336}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0.31}
  m_Radius: 0.3
--- !u!1 &8779301100313972929
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 969109904267124478}
  - component: {fileID: 253337148338995042}
  m_Layer: 0
  m_Name: Triangle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &969109904267124478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8779301100313972929}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.053, z: 0}
  m_LocalScale: {x: 1, y: -1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6069023758706627798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &253337148338995042
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8779301100313972929}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 1546342031
  m_SortingLayer: 1
  m_SortingOrder: 0
  m_Sprite: {fileID: -1211370019, guid: 6ad6a3dfc17c0b1409052558fc3e7c14, type: 3}
  m_Color: {r: 0, g: 0, b: 0, a: 0.4}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
