using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Sirenix.OdinInspector;
using MEC;
using System.Collections.Generic;

public class PlayerStatsUI : MonoBehaviour
{
    [Title("Player Components")]
    [SerializeField, Required] private PlayerStats playerStats;
    [Serial<PERSON><PERSON>ield, Required] private PlayerHealth playerHealth;
    
    [Title("Health UI Components")]
    [SerializeField, Required] private Image healthFillImage;
    [SerializeField, Required] private TMP_Text healthText;
    
    [Title("Mana UI Components")]
    [SerializeField] private Image manaFillImage;
    [SerializeField] private TMP_Text manaText;
    
    [Title("Experience UI Components")]
    [SerializeField, Required] private Slider xpSlider;
    [SerializeField, Required] private TMP_Text xpText;
    
    [Title("Optional Level Display")]
    [SerializeField] private TMP_Text levelText;
    
    [Title("Animation Settings")]
    [SerializeField, Range(0.1f, 2f)] private float animationDuration = 0.3f;
    [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    
    // Animation state tracking (zero-GC)
    private CoroutineHandle healthAnimationHandle;
    private CoroutineHandle manaAnimationHandle;
    private float targetHealthFill;
    private float targetManaFill;
    private bool isHealthAnimating;
    private bool isManaAnimating;
    
    private void Start()
    {
        // Auto-find components if not assigned
        if (playerStats == null)
        {
            playerStats = GetComponent<PlayerStats>();
        }
        
        if (playerHealth == null)
        {
            playerHealth = GetComponent<PlayerHealth>();
        }
    }
    
    private void OnEnable()
    {
        bool hasPlayerStats = playerStats != null;
        bool hasPlayerHealth = playerHealth != null;
        
        if (hasPlayerStats && hasPlayerHealth)
        {
            // Subscribe to PlayerHealth events for health
            playerHealth.OnHealthChanged.AddListener(UpdateHealthUI);
            
            // Subscribe to PlayerStats events for mana and XP (only if mana UI is assigned)
            if (manaFillImage != null || manaText != null)
            {
                playerStats.OnManaChanged.AddListener(UpdateManaUI);
            }
            
            playerStats.OnXPChanged.AddListener(UpdateXPUI);
            
            // Subscribe to level changes if level text is assigned
            if (levelText != null)
            {
                playerStats.OnLevelUp.AddListener(UpdateLevelUI);
            }
            
            // Initialize UI with current values
            InitializeUI();
        }
        else
        {
            if (!hasPlayerStats)
                Debug.LogError("PlayerStats reference is null! Please assign it in the inspector.", this);
            if (!hasPlayerHealth)
                Debug.LogError("PlayerHealth reference is null! Please assign it in the inspector.", this);
        }
    }
    
    private void OnDisable()
    {
        // Stop all running animations to prevent coroutine leaks
        if (healthAnimationHandle.IsValid)
        {
            Timing.KillCoroutines(healthAnimationHandle);
            healthAnimationHandle = default;
        }
        
        if (manaAnimationHandle.IsValid)
        {
            Timing.KillCoroutines(manaAnimationHandle);
            manaAnimationHandle = default;
        }
        
        // Reset animation states
        isHealthAnimating = false;
        isManaAnimating = false;
        
        if (playerHealth != null)
        {
            // Unsubscribe from PlayerHealth events
            playerHealth.OnHealthChanged.RemoveListener(UpdateHealthUI);
        }
        
        if (playerStats != null)
        {
            // Unsubscribe from PlayerStats events (only if we subscribed)
            if (manaFillImage != null || manaText != null)
            {
                playerStats.OnManaChanged.RemoveListener(UpdateManaUI);
            }
            
            playerStats.OnXPChanged.RemoveListener(UpdateXPUI);
            
            if (levelText != null)
            {
                playerStats.OnLevelUp.RemoveListener(UpdateLevelUI);
            }
        }
    }
    
    private void InitializeUI()
    {
        // Initialize all UI components with current values
        if (playerHealth != null)
        {
            UpdateHealthUI(playerHealth.currentHealth, playerHealth.maxHealth);
        }
        
        if (playerStats != null)
        {
            // Initialize mana UI only if components are assigned
            if (manaFillImage != null || manaText != null)
            {
                UpdateManaUI(playerStats.currentMana, playerStats.GetMaxMana());
            }
            
            UpdateXPUI(playerStats.currentXP, playerStats.xpToNextLevel);
            
            if (levelText != null)
            {
                UpdateLevelUI(playerStats.Level);
            }
        }
    }
    
    private void UpdateHealthUI(float current, float max)
    {
        // Clamp values to prevent invalid states
        current = Mathf.Max(0f, current);
        max = Mathf.Max(1f, max);
        
        // Calculate target fill amount (normalized 0-1)
        targetHealthFill = current / max;
        
        // Update health fill image with smooth animation
        if (healthFillImage != null)
        {
            // Stop previous animation if running
            if (healthAnimationHandle.IsValid)
            {
                Timing.KillCoroutines(healthAnimationHandle);
            }
            
            // Start new animation
            healthAnimationHandle = Timing.RunCoroutine(AnimateHealthFill());
        }
        
        // Update health text using garbage-free method with "current/max" format
        if (healthText != null)
        {
            healthText.SetTextNoAlc(Mathf.RoundToInt(current), "", "/" + Mathf.RoundToInt(max));
        }
    }
    
    private void UpdateManaUI(float current, float max)
    {
        // Clamp values to prevent invalid states
        current = Mathf.Max(0f, current);
        max = Mathf.Max(1f, max);
        
        // Calculate target fill amount (normalized 0-1)
        targetManaFill = current / max;
        
        // Update mana fill image with smooth animation
        if (manaFillImage != null)
        {
            // Stop previous animation if running
            if (manaAnimationHandle.IsValid)
            {
                Timing.KillCoroutines(manaAnimationHandle);
            }
            
            // Start new animation
            manaAnimationHandle = Timing.RunCoroutine(AnimateManaFill());
        }
        
        // Update mana text using garbage-free method with "current/max" format
        if (manaText != null)
        {
            manaText.SetTextNoAlc(Mathf.RoundToInt(current), "", "/" + Mathf.RoundToInt(max));
        }
    }
    
    private void UpdateXPUI(float current, float max)
    {
        // Clamp values to prevent invalid states
        current = Mathf.Max(0f, current);
        max = Mathf.Max(1f, max);
        
        // Update XP slider (normalized 0-1)
        if (xpSlider != null)
        {
            xpSlider.value = current / max;
        }
        
        // Update XP text using garbage-free method with "current/max" format
        if (xpText != null)
        {
            xpText.SetTextNoAlc(Mathf.RoundToInt(current), "", "/" + Mathf.RoundToInt(max));
        }
    }
    
    private void UpdateLevelUI(int level)
    {
        if (levelText != null)
        {
            levelText.SetTextNoAlc(level);
        }
    }
    
    /// <summary>
    /// Animates health fill image from current value to target with smooth easing
    /// </summary>
    private IEnumerator<float> AnimateHealthFill()
    {
        if (healthFillImage == null) yield break;
        
        isHealthAnimating = true;
        float startFill = healthFillImage.fillAmount;
        float elapsedTime = 0f;
        
        // Handle instant updates for same values (zero-GC early exit)
        if (Mathf.Approximately(startFill, targetHealthFill))
        {
            isHealthAnimating = false;
            yield break;
        }
        
        while (elapsedTime < animationDuration)
        {
            float normalizedTime = elapsedTime / animationDuration;
            float curveValue = animationCurve.Evaluate(normalizedTime);
            
            // Interpolate fill amount using the animation curve
            healthFillImage.fillAmount = Mathf.Lerp(startFill, targetHealthFill, curveValue);
            
            elapsedTime += Timing.DeltaTime;
            yield return Timing.WaitForOneFrame;
        }
        
        // Ensure exact final value
        healthFillImage.fillAmount = targetHealthFill;
        isHealthAnimating = false;
    }
    
    /// <summary>
    /// Animates mana fill image from current value to target with smooth easing
    /// </summary>
    private IEnumerator<float> AnimateManaFill()
    {
        if (manaFillImage == null) yield break;
        
        isManaAnimating = true;
        float startFill = manaFillImage.fillAmount;
        float elapsedTime = 0f;
        
        // Handle instant updates for same values (zero-GC early exit)
        if (Mathf.Approximately(startFill, targetManaFill))
        {
            isManaAnimating = false;
            yield break;
        }
        
        while (elapsedTime < animationDuration)
        {
            float normalizedTime = elapsedTime / animationDuration;
            float curveValue = animationCurve.Evaluate(normalizedTime);
            
            // Interpolate fill amount using the animation curve
            manaFillImage.fillAmount = Mathf.Lerp(startFill, targetManaFill, curveValue);
            
            elapsedTime += Timing.DeltaTime;
            yield return Timing.WaitForOneFrame;
        }
        
        // Ensure exact final value
        manaFillImage.fillAmount = targetManaFill;
        isManaAnimating = false;
    }
    
    #if UNITY_EDITOR
    [Title("Debug Tools")]
    [FoldoutGroup("Debug")]
    [Button("Test Health Change", ButtonSizes.Small)]
    private void TestHealthChange()
    {
        if (playerHealth != null)
        {
            var damageInfo = DamageInfo.FromSingleType(10f, DamageType.Physical, false, 1f, "TestUI");
            playerHealth.TakeDamage(damageInfo);
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Mana Change", ButtonSizes.Small)]
    private void TestManaChange()
    {
        if (playerStats != null)
        {
            playerStats.SpendMana(10f);
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test XP Change", ButtonSizes.Small)]
    private void TestXPChange()
    {
        if (playerStats != null)
        {
            playerStats.AddXP(25f);
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Heal Player", ButtonSizes.Small)]
    private void TestHeal()
    {
        if (playerHealth != null)
        {
            playerHealth.Heal(20f);
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Restore Mana", ButtonSizes.Small)]
    private void TestRestoreMana()
    {
        if (playerStats != null)
        {
            playerStats.RestoreMana(20f);
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Animation Settings", ButtonSizes.Small)]
    private void TestAnimationSettings()
    {
        Debug.Log($"Animation Duration: {animationDuration}s | Curve Keys: {animationCurve.keys.Length}");
    }
    
    [FoldoutGroup("Debug")]
    [Button("Force Stop Animations", ButtonSizes.Small)]
    private void ForceStopAnimations()
    {
        if (healthAnimationHandle.IsValid)
        {
            Timing.KillCoroutines(healthAnimationHandle);
            isHealthAnimating = false;
        }
        
        if (manaAnimationHandle.IsValid)
        {
            Timing.KillCoroutines(manaAnimationHandle);
            isManaAnimating = false;
        }
        
        Debug.Log("All UI animations stopped.");
    }
    
    [FoldoutGroup("Debug")]
    [ShowInInspector]
    [PropertySpace]
    private string UIStatus => 
        $"Health UI: {(healthFillImage != null && healthText != null ? "✓" : "✗")}\n" +
        $"Mana UI: {(manaFillImage != null && manaText != null ? "✓" : "✗")}\n" +
        $"XP UI: {(xpSlider != null && xpText != null ? "✓" : "✗")}\n" +
        $"Level UI: {(levelText != null ? "✓" : "✗")}\n" +
        $"PlayerStats: {(playerStats != null ? "✓" : "✗")}\n" +
        $"PlayerHealth: {(playerHealth != null ? "✓" : "✗")}\n" +
        $"Health Animating: {(isHealthAnimating ? "▶️" : "⏸️")}\n" +
        $"Mana Animating: {(isManaAnimating ? "▶️" : "⏸️")}";
    #endif
}