%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: AreaOfEffect
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SupportGemData
  icon: {fileID: 21300000, guid: 675102478193e734889408763d205fde, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Area of Effect
  description: Adds area damage to supported skills
  rarity: 2
  requiresUnlock: 0
  unlockCondition: {fileID: 0}
  useIntegerScaling: 0
  commonStatMultiplier: 0.7
  uncommonStatMultiplier: 0.85
  rareStatMultiplier: 1
  epicStatMultiplier: 1.15
  uniqueStatMultiplier: 1.3
  commonIntMultiplier: 0.5
  uncommonIntMultiplier: 0.75
  rareIntMultiplier: 1
  epicIntMultiplier: 1.5
  uniqueIntMultiplier: 2
  compatibleTags: 2
  damageIncreased: 0
  damageMore: 1
  cooldownMultiplier: 1.3
  manaCostMultiplier: 1.4
  skillDurationMultiplier: 1
  attackSpeedMultiplier: 1
  addedCritChance: 0
  critMultiplierModifier: 1
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 1
  addsMultipleProjectiles: 0
  extraProjectiles: 2
  projectileSpreadAngle: 15
  useParallelProjectiles: 0
  projectileLateralOffset: 0.6
  useRandomSpread: 0
  chainCount: 2
  areaIncreased: 30
  areaMore: 1
  addsFork: 0
  forkCount: 2
  igniteEffectivenessMultiplier: 1
  igniteDurationMultiplier: 1
  freezeEffectivenessMultiplier: 1
  freezeDurationMultiplier: 1
  bleedEffectivenessMultiplier: 1
  bleedDurationMultiplier: 1
  shockEffectivenessMultiplier: 1
  shockRangeMultiplier: 1
  forkAngle: 30
  addsSpellEcho: 0
  echoDelay: 0.4
  echoCount: 1
  echoSpreadRadius: 0
  addsAutonomous: 0
  autonomousRange: 8
  autonomousUpdateInterval: 0.5
  isConversionGem: 0
  conversionPercent: 50
  convertFromType: 0
  convertToType: 1
  physicalDamageIncreased: 0
  physicalDamageMore: 1
  fireDamageIncreased: 0
  fireDamageMore: 1
  iceDamageIncreased: 0
  iceDamageMore: 1
  lightningDamageIncreased: 0
  lightningDamageMore: 1
  elementalDamageIncreased: 0
  elementalDamageMore: 1
  armorPenetrationPercent: 0
  flatArmorPenetration: 0
  firePenetration: 0
  coldPenetration: 0
  lightningPenetration: 0
  elementalPenetration: 0
  preventsElementalDamage: 0
  addsRuthlessHits: 0
  ruthlessHitInterval: 3
  ruthlessDamageMultiplier: 2
