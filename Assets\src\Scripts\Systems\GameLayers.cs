using UnityEngine;

/// <summary>
/// Unity native layer constants and LayerMask definitions
/// Replaces the custom CollisionLayers enum system for better Unity integration
/// 
/// Layer Setup - Matches Unity's TagManager configuration:
/// Layer 0:  Default
/// Layer 1:  TransparentFX
/// Layer 2:  Ignore Raycast
/// Layer 3:  [Empty]
/// Layer 4:  Water
/// Layer 5:  UI
/// Layer 6:  Players
/// Layer 7:  Enemy
/// Layer 8:  PlayerProjectile
/// Layer 9:  EnemyProjectile
/// Layer 10: Wall
/// Layer 11: Trigger
/// Layer 12: PickUp
/// Layer 13: Environment
/// Layer 14: Interactable
/// Layer 15: Vegetation
/// Layer 16: Breach
/// Layer 17: Shop
/// </summary>
public static class GameLayers
{
    // Layer indices (matching Unity's TagManager configuration!)
    public const int Player = 6;           // "Players" layer
    public const int Enemy = 7;            // "Enemy" layer
    public const int PlayerProjectile = 8; // "PlayerProjectile" layer
    public const int EnemyProjectile = 9;  // "EnemyProjectile" layer
    public const int Wall = 10;            // "Wall" layer
    public const int Trigger = 11;         // "Trigger" layer
    public const int Pickup = 12;          // "PickUp" layer
    public const int Environment = 13;     // "Environment" layer
    public const int Interactable = 14;    // "Interactable" layer
    public const int Vegetation = 15;      // "Vegetation" layer
    public const int Breach = 16;          // "Breach" layer
    public const int Shop = 17;            // "Shop" layer
    
    // Pre-calculated LayerMasks for performance (no runtime bitshift operations)
    public static readonly LayerMask PlayerMask = 1 << Player;
    public static readonly LayerMask EnemyMask = 1 << Enemy;
    public static readonly LayerMask PlayerProjectileMask = 1 << PlayerProjectile;
    public static readonly LayerMask EnemyProjectileMask = 1 << EnemyProjectile;
    public static readonly LayerMask WallMask = 1 << Wall;
    public static readonly LayerMask TriggerMask = 1 << Trigger;
    public static readonly LayerMask PickupMask = 1 << Pickup;
    public static readonly LayerMask EnvironmentMask = 1 << Environment;
    public static readonly LayerMask InteractableMask = 1 << Interactable;
    public static readonly LayerMask VegetationMask = 1 << Vegetation;
    public static readonly LayerMask BreachMask = 1 << Breach;
    public static readonly LayerMask ShopMask = 1 << Shop;
    
    // Common layer combinations (replaces CollisionLayers combinations)
    public static readonly LayerMask AllProjectilesMask = PlayerProjectileMask | EnemyProjectileMask;
    public static readonly LayerMask AllCharactersMask = PlayerMask | EnemyMask;
    public static readonly LayerMask AllSolidMask = WallMask | EnvironmentMask;
    
    // Target-specific masks for common collision detection scenarios
    public static readonly LayerMask DamageableByPlayerMask = EnemyMask | EnvironmentMask | VegetationMask;
    public static readonly LayerMask DamageableByEnemyMask = PlayerMask;
    public static readonly LayerMask PlayerDetectionMask = PlayerMask; // For enemy AI
    public static readonly LayerMask EnemyDetectionMask = EnemyMask; // For player abilities
    public static readonly LayerMask SolidCollisionMask = WallMask | EnvironmentMask; // For movement blocking
    
    // Interaction-specific masks
    public static readonly LayerMask PlayerInteractionMask = PickupMask | InteractableMask | ShopMask | TriggerMask;
    public static readonly LayerMask BreachTargetMask = VegetationMask | PlayerMask;
    
    // Helper methods for type-safe layer checking (replaces enum comparisons)
    public static bool IsPlayer(int layer) => layer == Player;
    public static bool IsEnemy(int layer) => layer == Enemy;
    public static bool IsPlayerProjectile(int layer) => layer == PlayerProjectile;
    public static bool IsEnemyProjectile(int layer) => layer == EnemyProjectile;
    public static bool IsWall(int layer) => layer == Wall;
    public static bool IsTrigger(int layer) => layer == Trigger;
    public static bool IsPickup(int layer) => layer == Pickup;
    public static bool IsEnvironment(int layer) => layer == Environment;
    public static bool IsInteractable(int layer) => layer == Interactable;
    public static bool IsVegetation(int layer) => layer == Vegetation;
    public static bool IsBreach(int layer) => layer == Breach;
    public static bool IsShop(int layer) => layer == Shop;
    
    // Helper methods for common layer group checks
    public static bool IsProjectile(int layer) => layer == PlayerProjectile || layer == EnemyProjectile;
    public static bool IsCharacter(int layer) => layer == Player || layer == Enemy;
    public static bool IsSolid(int layer) => layer == Wall || layer == Environment;
    public static bool IsDamageableByPlayer(int layer) => layer == Enemy || layer == Environment || layer == Vegetation;
    public static bool IsDamageableByEnemy(int layer) => layer == Player;
    
    /// <summary>
    /// Check if a layer is included in a LayerMask
    /// Utility method for runtime layer mask checking
    /// </summary>
    public static bool IsInLayerMask(int layer, LayerMask layerMask)
    {
        return (layerMask.value & (1 << layer)) != 0;
    }
    
    /// <summary>
    /// Get LayerMask for a specific layer index
    /// Utility method for dynamic LayerMask creation
    /// </summary>
    public static LayerMask GetLayerMask(int layer)
    {
        return 1 << layer;
    }
    
    /// <summary>
    /// Convert old CollisionLayers enum values to Unity layer indices
    /// Runtime migration helper for existing ScriptableObject assets
    /// </summary>
    public static int FromCollisionLayer(int collisionLayerValue)
    {
        // CollisionLayers used bitshift values: Player = 1 << 0, Enemy = 1 << 1, etc.
        // Unity layers are direct indices: Player = 6, Enemy = 7, etc.
        switch (collisionLayerValue)
        {
            case 1: return Player;        // 1 << 0 -> Player
            case 2: return Enemy;         // 1 << 1 -> Enemy  
            case 4: return PlayerProjectile; // 1 << 2 -> PlayerProjectile
            case 8: return EnemyProjectile;  // 1 << 3 -> EnemyProjectile
            case 16: return Wall;         // 1 << 4 -> Wall
            case 32: return Trigger;      // 1 << 5 -> Trigger
            case 64: return Pickup;       // 1 << 6 -> Pickup
            case 128: return Environment; // 1 << 7 -> Environment
            case 256: return Interactable; // 1 << 8 -> Interactable
            case 512: return Vegetation;  // 1 << 9 -> Vegetation
            case 1024: return Breach;     // 1 << 10 -> Breach
            case 2048: return Shop;       // 1 << 11 -> Shop
            default: 
                Debug.LogWarning($"Unknown CollisionLayers value {collisionLayerValue}, defaulting to PlayerProjectile layer");
                return PlayerProjectile; // Safe default
        }
    }
}